import numpy as np
import matplotlib.pyplot as plt
import os

# Path to the saved cardiac data
cardiac_data_path = "/Users/<USER>/Documents/important document backup/Local python code/MRXCAT2/mrxcat-2.0-main/male169/cardiac_region_new/cardiac_volume.npz"

# Load all parameter maps
print("Loading cardiac parameter maps...")
data = np.load(cardiac_data_path)
print(f"data shape: {data['Labels'].shape }")
# Display available parameters
print(f"Available parameters: {list(data.keys())}")

# Get the middle slice index for visualization
z_slice = data['Labels'].shape[2] // 2
print(f"Displaying slice {z_slice} of {data['Labels'].shape[2]}")

# Define parameter visualization settings
param_settings = {
    'PD': {'cmap': 'gray', 'title': 'Proton Density', 'vmin': None, 'vmax': None},
    'T1': {'cmap': 'hot', 'title': 'T1 (ms)', 'vmin': 0, 'vmax': 2000},
    'T2': {'cmap': 'viridis', 'title': 'T2 (ms)', 'vmin': 0, 'vmax': 150},
    'T2star': {'cmap': 'inferno', 'title': 'T2* (ms)', 'vmin': 0, 'vmax': 100},
    'T2star_plus': {'cmap': 'inferno', 'title': 'T2*+ (ms)', 'vmin': 0, 'vmax': 100},
    'Labels': {'cmap': 'tab10', 'title': 'Tissue Labels', 'vmin': None, 'vmax': None},
    'B0': {'cmap': 'jet', 'title': 'B0 Field (Hz)', 'vmin': -400, 'vmax': 400},
    'ShimmedB0': {'cmap': 'jet', 'title': 'Shim B0 Field', 'vmin': -400, 'vmax': 400}
}

# Create figure for visualization
n_params = len(param_settings)
n_cols = 3  # Define number of columns
n_rows = (n_params + n_cols - 1) // n_cols  # Calculate required rows

plt.figure(figsize=(15, 4 * n_rows))

# Display each parameter map
for i, (param_name, settings) in enumerate(param_settings.items()):
    plt.subplot(n_rows, n_cols, i + 1)
    
    # Check if parameter exists in the data
    if param_name in data:
        param_data = data[param_name]
        param_data = np.transpose(param_data, [2, 1, 0])
        # Get image slice
        image_slice = param_data[:, :, z_slice]
        
        # Display the image
        im = plt.imshow(image_slice, 
                        cmap=settings['cmap'],
                        vmin=settings['vmin'],
                        vmax=settings['vmax'])
        
        plt.colorbar(im, fraction=0.046, pad=0.04)
        plt.title(f"{settings['title']}\n(min: {np.min(image_slice):.1f}, max: {np.max(image_slice):.1f})")
    else:
        plt.text(0.5, 0.5, f"{param_name} not found", 
                ha='center', va='center', 
                transform=plt.gca().transAxes)
        plt.title(f"Missing: {settings['title']}")
    
    plt.axis('off')

# Adjust layout and display
plt.tight_layout()
plt.show()

# Display cardiac anatomy with B0 overlay
if 'Labels' in data and 'B0' in data:
    plt.figure(figsize=(10, 8))
    
    # Create a mask for cardiac structures
    cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
    for label in [1, 2, 5, 6]:  # LV wall, RV wall, LV blood, RV blood
        cardiac_mask |= (data['Labels'][:,:,z_slice] == label)
    
    # Create a color-coded anatomy image
    anatomy = np.zeros((*data['Labels'][:,:,z_slice].shape, 3))
    anatomy[data['Labels'][:,:,z_slice] == 1] = [1, 0, 0]  # LV wall - red
    anatomy[data['Labels'][:,:,z_slice] == 5] = [1, 0.7, 0.7]  # LV blood - light red
    anatomy[data['Labels'][:,:,z_slice] == 2] = [0, 0, 1]  # RV wall - blue
    anatomy[data['Labels'][:,:,z_slice] == 6] = [0.7, 0.7, 1]  # RV blood - light blue
    
    # Display base anatomy
    plt.imshow(anatomy)
    
    # Overlay B0 field on cardiac region
    b0_display = data['B0'][:,:,z_slice].copy()
    b0_display[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent
    
    b0_overlay = plt.imshow(b0_display, cmap='coolwarm', alpha=0.7, 
                           vmin=-30, vmax=30)
    plt.colorbar(b0_overlay, label='B0 Field (Hz)')
    
    plt.title('B0 Field Overlay on Cardiac Anatomy')
    plt.axis('off')
    plt.tight_layout()
    plt.show()

# Create cardiac mask
cardiac_mask = np.zeros_like(data['Labels'], dtype=bool)
for label in [1, 2, 5, 6]:  # LV wall, RV wall, LV blood, RV blood
    cardiac_mask |= (data['Labels'] == label)

# Store original B0 field
original_b0 = data['B0'].copy()

# Plot before 0th shim
plt.figure(figsize=(15, 5))

# Before shim - show B0 field on cardiac region
plt.subplot(1, 3, 1)
b0_before = original_b0.copy()
b0_before[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent
plt.imshow(b0_before[:,:,z_slice], cmap='coolwarm', alpha=0.7, vmin=-30, vmax=30)
plt.colorbar(label='B0 Field (Hz)')
plt.title('Before 0th Shim')
plt.axis('off')

# Calculate and apply 0th shim
median_b0_cardiac = np.median(original_b0[cardiac_mask])
shimmed_b0 = original_b0 - median_b0_cardiac

# After shim - show B0 field on cardiac region
plt.subplot(1, 3, 2)
b0_after = shimmed_b0.copy()
b0_after[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent
plt.imshow(b0_after[:,:,z_slice], cmap='coolwarm', alpha=0.7, vmin=-30, vmax=30)
plt.colorbar(label='B0 Field (Hz)')
plt.title('After 0th Shim')
plt.axis('off')

# Difference plot
plt.subplot(1, 3, 3)
b0_diff = shimmed_b0 - original_b0
b0_diff[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent
plt.imshow(b0_diff[:,:,z_slice], cmap='RdBu_r', alpha=0.7, vmin=-10, vmax=10)
plt.colorbar(label='B0 Difference (Hz)')
plt.title('Difference (After - Before)')
plt.axis('off')

plt.tight_layout()
plt.show()

# Print statistics
print(f"Before 0th shim - Cardiac B0 statistics:")
print(f"  Mean: {np.mean(original_b0[cardiac_mask]):.2f} Hz")
print(f"  Median: {np.median(original_b0[cardiac_mask]):.2f} Hz")
print(f"  Std: {np.std(original_b0[cardiac_mask]):.2f} Hz")
print(f"  Range: [{np.min(original_b0[cardiac_mask]):.2f}, {np.max(original_b0[cardiac_mask]):.2f}] Hz")

print(f"\nAfter 0th shim - Cardiac B0 statistics:")
print(f"  Mean: {np.mean(shimmed_b0[cardiac_mask]):.2f} Hz")
print(f"  Median: {np.median(shimmed_b0[cardiac_mask]):.2f} Hz")
print(f"  Std: {np.std(shimmed_b0[cardiac_mask]):.2f} Hz")
print(f"  Range: [{np.min(shimmed_b0[cardiac_mask]):.2f}, {np.max(shimmed_b0[cardiac_mask]):.2f}] Hz")

print(f"\n0th shim offset applied: {median_b0_cardiac:.2f} Hz")

# Update the data with shimmed B0 field
# Note: NpzFile objects don't support item assignment, so we need to create a new dictionary
# with the updated B0 field and save it to a new file if needed
shimmed_data = dict(data)
shimmed_data['B0'] = shimmed_b0

def create_improved_interactive_viewer_axial(data_input, tissue_types=None, best_slice=0, maskLabels=None):
    """
    Create an interactive axial viewer. If tissue_types is provided, the function displays
    a colored label image with overlaid edges and a legend. Otherwise, it displays the raw slice with a colorbar.
    
    Parameters:
      data_input   : numpy array, the 3D data (assumed shape [X, Y, Z])
      tissue_types : dict mapping tissue names to integer labels, or None for raw display
      best_slice   : int, initial axial slice index (along the first axis)
      maskLabels   : (unused) additional label definitions
    """
    import matplotlib.pyplot as plt
    import matplotlib.colors as mcolors
    import matplotlib.patches as mpatches
    import seaborn as sns
    from scipy import ndimage
    import ipywidgets as widgets
    from ipywidgets import interactive_output, VBox, IntSlider
    import numpy as np

    def update(slice_idx):
        # Create a new figure.
        fig = plt.figure(figsize=(12, 10))
        
        # Extract the axial slice (data_input[slice_idx, :, :])
        current_slice = data_input[slice_idx, :, :].copy()
        
        if tissue_types is None:
            # If tissue_types is not provided, display the raw slice with a colorbar.
            ax = fig.add_subplot(111)
            im = ax.imshow(current_slice, cmap='gray')
            ax.set_title(f'Axial Slice {slice_idx}', fontsize=18)
            ax.axis('off')
            plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        
        else:
            # Prepare label mapping and color palette
            listed_labels = list(tissue_types.values())
            label_to_idx = {label: i for i, label in enumerate(listed_labels)}
            n_tissues = len(tissue_types)
            
            # Base palette (using seaborn for high contrast colors)
            base_colors = sns.color_palette("bright", 10)
            extended_colors = sns.color_palette("husl", max(0, n_tissues-10))
            distinct_colors = base_colors + extended_colors
            # Build custom colormap: first color is transparent for background
            cmap_colors = [(0, 0, 0, 0)]
            for i in range(n_tissues):
                cmap_colors.append(distinct_colors[i])
            custom_cmap = mcolors.ListedColormap(cmap_colors)
            
            # Create a normalized array mapping each label to a unique value
            # Keep only voxels with labels in tissue_types; else set to background (0)
            mask = np.isin(current_slice, listed_labels)
            filtered_slice = np.where(mask, current_slice, 0)
            custom_colored_slice = np.zeros_like(filtered_slice, dtype=float)
            for label in listed_labels:
                if (filtered_slice == label).any():
                    custom_colored_slice[filtered_slice == label] = label_to_idx[label] + 1
            if len(label_to_idx) > 0:
                custom_colored_slice = custom_colored_slice / (len(label_to_idx) + 1)
            
            # Reverse the second dimension (columns)
            custom_colored_slice = custom_colored_slice[:, ::-1]
            
            # Create subplots: left for image, right for legend.
            ax_img = fig.add_subplot(121)
            ax_legend = fig.add_subplot(122)
            
            # Display image using the custom colormap
            ax_img.imshow(custom_colored_slice, cmap=custom_cmap, vmin=0, vmax=1)
            ax_img.set_title(f'Combined Tissue Labels (Axial Slice {slice_idx})', fontsize=18)
            ax_img.axis('off')
            
            # Compute edge overlay (for tissue boundaries)
            edge_slice = np.zeros_like(filtered_slice)
            for label in listed_labels:
                if (filtered_slice == label).any():
                    tissue_mask = (filtered_slice == label).astype(np.uint8)
                    edges = ndimage.sobel(tissue_mask, axis=0)**2 + ndimage.sobel(tissue_mask, axis=1)**2
                    edge_slice[edges > 0] = 1
            # Reverse the edges as well
            edge_slice = edge_slice[:, ::-1]
            ax_img.contour(edge_slice, levels=[0.5], colors='black', linewidths=0.2, alpha=0.4)
            
            # Optionally zoom in on the region where tissue exists
            rows = filtered_slice.max(axis=1) > 0
            cols = filtered_slice.max(axis=0) > 0
            if rows.sum() and cols.sum():
                r_idx = np.where(rows)[0]
                c_idx = np.where(cols)[0]
                rmin, rmax = r_idx[0], r_idx[-1]
                cmin, cmax = c_idx[0], c_idx[-1]
                padding = 20
                rmin = max(0, rmin - padding)
                rmax = min(filtered_slice.shape[0] - 1, rmax + padding)
                cmin = max(0, cmin - padding)
                cmax = min(filtered_slice.shape[1] - 1, cmax + padding)
                ax_img.set_xlim(cmin, cmax)
                ax_img.set_ylim(rmin, rmax)
            
            # Build legend entries for tissue groups
            legend_entries = []
            # Group the tissues as desired (here we simply list all tissues)
            legend_entries.append((mpatches.Patch(color='white', alpha=0), 'Tissues:'))
            for name, label in tissue_types.items():
                if (filtered_slice == label).any():
                    # Get the corresponding color from distinct_colors.
                    # Assumes tissue_types keys are ordered or you can modify as needed.
                    idx = list(tissue_types.keys()).index(name)
                    color = distinct_colors[idx]
                    legend_entries.append((mpatches.Patch(color=color), name))
            if legend_entries:
                patches, leg_labels = zip(*legend_entries)
                ax_legend.legend(patches, leg_labels, loc='center', fontsize=14,
                                 frameon=True, fancybox=True, shadow=True,
                                 title="Tissue Types", title_fontsize=16)
            ax_legend.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    slider = IntSlider(min=0, max=data_input.shape[0]-1, value=best_slice,
                       description='Axial Slice:', continuous_update=False)
    ui = VBox([slider])
    out = interactive_output(update, {'slice_idx': slider})
    display(ui, out)
    
param_data_input = data['T2']
param_data_input = np.transpose(param_data_input,[2, 1, 0])
print(f':{param_data_input.shape}')  # Ensure the data is loaded correctly
best_slice = param_data_input.shape[0] // 2  # Get the middle slice index
# Assuming tissue_types and best_slice are defined
# Call the axial viewer (assuming data_input, tissue_types, best_slice, and maskLabels are defined):
create_improved_interactive_viewer_axial(param_data_input, None, best_slice, None)

def create_interactive_oblique_viewer_3D(data_input, tissue_types, best_slice, maskLabels, Lu=200, Lv=100,theta = -45):
    """
    Interactive viewer that re-slices the 3D volume along an oblique plane.
    
    The new slicing plane is defined as follows:
      • Center at:  center = (best_slice, Y/2, X/2)  [using volume order (z,y,x)]
      • new_x axis: 45° direction in the axial (y–x) plane:
                     new_x = (0, cos45, sin45)
      • new_y axis: Along the original z-axis:
                     new_y = (1, 0, 0)
      • new_z axis: Perpendicular, computed as:
                     new_z = cross(new_x, new_y) = (0, cos45, -sin45)
    
    The 2D oblique slice is then extracted from the volume via:
         original_coord = center + u * new_x + v * new_y + offset * new_z,
    where u ∈ [–Lu/2, Lu/2], v ∈ [–Lv/2, Lv/2].
    
    The interactive slider controls offset (displacement along new_z).
    
    Parameters:
      data_input : 3D numpy array with shape (Z, Y, X)
      tissue_types : dict (unused here; for consistency)
      best_slice : int, the axial slice index to serve as center (e.g. 604)
      maskLabels : unused here
      Lu : length (in pixels) along new_x (the oblique direction in the axial plane)
      Lv : length (in pixels) along new_y (the original z direction)
    """
    import numpy as np
    import matplotlib.pyplot as plt
    from scipy import ndimage
    import ipywidgets as widgets
    from ipywidgets import interactive_output, VBox, IntSlider

    # Determine volume dimensions and center point.
    Z, Y, X = data_input.shape
    center = np.array([best_slice, Y/2, X/2])  # center = (z, y, x)
    
    # Define new coordinate directions.
    # new_x: 45° in the axial (y,x) plane, no z component.
    # theta = -45
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # e.g. (0, 0.7071, 0.7071)
    # new_y: along the original z-axis.
    new_y = np.array([1, 0, 0])
    # new_z: perpendicular to both.
    new_z = np.cross(new_x, new_y)  # = (0, cos45, -sin45) ≈ (0, 0.7071, -0.7071)
    
    def extract_oblique_slice(offset):
        """
        Extract a 2D oblique slice from the 3D volume.
        
        For pixel coordinates (u, v) in the new plane, mapping is given by:
           original = center + u * new_x + v * new_y + offset * new_z.
        u is in [-Lu/2, Lu/2] and v in [-Lv/2, Lv/2].
        Out-of-bound values are padded with 0.
        """
        Lu_pixels = int(Lu)
        Lv_pixels = int(Lv)
        # Create grid for new slice coordinates.
        u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
        v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
        U, V = np.meshgrid(u, v)  # shape (Lv_pixels, Lu_pixels)
        # Compute original coordinates.
        # Note: Original coordinate order: (z, y, x).
        Z_coord = center[0] + V * new_y[0] + offset * new_z[0]  # new_y contributes to z; new_z[0] is 0.
        Y_coord = center[1] + U * new_x[1] + offset * new_z[1]  # new_x[1] = cos45; new_z[1] = cos45.
        X_coord = center[2] + U * new_x[2] + offset * new_z[2]  # new_x[2] = sin45; new_z[2] = -sin45.
        coords = [Z_coord, Y_coord, X_coord]
        oblique_slice = ndimage.map_coordinates(data_input, coords, order=1, mode='constant', cval=0)
        return oblique_slice

    def update(offset):
        plt.clf()
        fig, (ax_left, ax_right) = plt.subplots(1, 2, figsize=(20, 10))
        
        # Left panel: show the original axial slice (at best_slice).
        axial = data_input[best_slice, :, :]
        ax_left.imshow(axial, cmap='gray')
        ax_left.set_title(f'Axial Slice {best_slice}', fontsize=16)
        ax_left.axis('off')
        # Compute intersection (line) of the oblique plane with axial plane (z = best_slice).
        # The intersection occurs when: center + u*new_x + v*new_y + offset*new_z has z = best_slice.
        # Since center_z = best_slice and new_y = (1,0,0), we have: best_slice + v = best_slice  => v = 0.
        # Thus, for varying u with v=0:
        u_line = np.linspace(-Lu/2, Lu/2, 100)
        v_line = np.zeros_like(u_line)  # v = 0
        # Then:
        y_line = center[1] + u_line * new_x[1] + offset * new_z[1]
        x_line = center[2] + u_line * new_x[2] + offset * new_z[2]
        ax_left.plot(x_line, y_line, 'r--', linewidth=2)
        
        # Right panel: oblique slice extracted from the volume.
        obslice = extract_oblique_slice(offset)
        ax_right.imshow(obslice, cmap='inferno', vmin=np.min(obslice), vmax=np.max(obslice))
        ax_right.set_title(f'Oblique Slice (offset = {offset}px)', fontsize=16)
        ax_right.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    slider = IntSlider(min=-50, max=50, step=1, value=0, description='Offset (px):', continuous_update=False)
    ui = VBox([slider])
    out = interactive_output(update, {'offset': slider})
    display(ui, out)

# Example usage:
# Here we choose slice 604 as the reference axial slice.
create_interactive_oblique_viewer_3D(param_data_input, None, 65, None, Lu=150, Lv=150,theta = -45)

import numpy as np
from scipy import ndimage

# Parameters for re-slicing
best_slice = 60         # axial slice used as center
Lu = 150                # length along new_x (in pixels)
Lv = 150                # length along new_y (in pixels)
Lz = 101                 # number of slices along new_z (choose an odd number for a central slice)
# d will range from -D/2 to D/2, so that the central slice (d=0) becomes the reference.
D = 100                   # total span in new_z (in pixels), for example 6 pixels -> d from -3 to 3
fixed_offset = -7  # fixed offset for the central slice in new_z, can be set to 0 for the middle slice

# Volume dimensions and center (data_input is assumed defined)
Z, Y, X = param_data_input.shape
center = np.array([best_slice, Y/2, X/2])  # order: (z, y, x)

# Define new coordinate axes.
# For an oblique plane with new_x along -45° in axial (y,x) plane:
theta = -45
theta_rad = np.deg2rad(theta)
new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])  # (0, cosθ, sinθ)
new_y = np.array([1, 0, 0])  # along original z-axis
# We want new_z = (0, cosθ, -sinθ) as specified.
new_z = -np.cross(new_x, new_y)  # ensures new_z = (0, cosθ, -sinθ)

# Now, incorporate the fixed offset by shifting the center.
shifted_center = center + fixed_offset * new_z
center = shifted_center  # update center to account for the fixed offset

# Set up new resliced volume dimensions (new volume coordinate grid):
Lu_pixels = int(Lu)
Lv_pixels = int(Lv)
Lz_pixels = int(Lz)

# Create grids for new coordinates:
# u: along new_x, v: along new_y, d: along new_z.
u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
d = np.linspace(-D/2, D/2, Lz_pixels)  # d coordinate

# Create a 3D meshgrid over (d, v, u)
# The order of axes for the new volume will be: new_z (d), new_y (v), new_x (u)
D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')  # shape (Lz, Lv, Lu)

# Map new coordinates (d, v, u) to original coordinates:
# original = center + d*new_z + v*new_y + u*new_x
orig_Z = center[0] + D_grid * new_z[0] + V_grid * new_y[0] + U_grid * new_x[0]
orig_Y = center[1] + D_grid * new_z[1] + V_grid * new_y[1] + U_grid * new_x[1]
orig_X = center[2] + D_grid * new_z[2] + V_grid * new_y[2] + U_grid * new_x[2]

coords = [orig_Z, orig_Y, orig_X]

# Reslice the original volume using linear interpolation and zero-padding for out-of-bound values.
ref_volume = ndimage.map_coordinates(param_data_input, coords, order=1, mode='constant', cval=0)

# Determine the central slice index in the new volume (along d)
ref_slice_index = Lz_pixels // 2

print("Oblique 3D volume stored in ref_volume with shape:", ref_volume.shape)
print("Central oblique slice index:", ref_slice_index)

create_interactive_oblique_viewer_3D(ref_volume, None, 51, None,  Lu=150, Lv=150,theta = -18)

new_best_slice = 51         # axial slice used as center
Lu_new = 150                # length along new_x (in pixels)
Lv_new = 150                # length along new_y (in pixels)
Lz_new = 101                 # number of slices along new_z (choose an odd number for a central slice)
# d will range from -D/2 to D/2, so that the central slice (d=0) becomes the reference.
D_new = 100                   # total span in new_z (in pixels), for example 6 pixels -> d from -3 to 3
fixed_offset = 0  # fixed offset for the central slice in new_z, can be set to 0 for the middle slice
new_theta = -18
# Determine dimensions of the current ref_volume
Lz_old, Lv_old, Lu_old = ref_volume.shape
# Define new center in ref_volume coordinates (order: (d, v, u)):
center_new = np.array([new_best_slice, Lv_old/2, Lu_old/2])

# Define new coordinate axes for the re-slicing.
# Here we interpret the in-plane (v,u) axes as follows:
#   new_x_re: in-plane direction at new_theta (relative to horizontal axis in ref_volume)
#            (Note: In our convention, the ref_volume image is indexed as [v, u])
#   new_y_re: taken along the original d-axis.
#   new_z_re: defined as perpendicular (we want to shift around the plane normal).
#
# We embed the fixed_offset by shifting the center along new_z_re.
new_theta_rad = np.deg2rad(new_theta)
new_x_re = np.array([0, np.cos(new_theta_rad), np.sin(new_theta_rad)])   # no d component
new_y_re = np.array([1, 0, 0])  # along the original d-axis (slice axis of ref_volume)
# Define new_z_re as perpendicular to both:
new_z_re = -np.cross(new_x_re, new_y_re)  # This yields new_z_re = (0, cos(new_theta), -sin(new_theta))

# Now, incorporate the fixed offset by shifting the center.
shifted_center = center_new + fixed_offset * new_z_re

# Set up new re-sliced volume coordinate grid.
Lu_pixels = int(Lu_new)
Lv_pixels = int(Lv_new)
Lz_pixels = int(Lz_new)

# Create grids for local coordinates:
# u: along new_x_re, v: along new_y_re, d: along new_z_re.
u = np.linspace(-Lu_new/2, Lu_new/2, Lu_pixels)
v = np.linspace(-Lv_new/2, Lv_new/2, Lv_pixels)
d = np.linspace(-D_new/2, D_new/2, Lz_pixels)  # d coordinate in the new system

# Meshgrid: order (d, v, u)
D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')

# Map new coordinates to ref_volume coordinates:
# For any point (d, v, u) in the new coordinate system:
#    coord = shifted_center + u * new_x_re + v * new_y_re + d * new_z_re
orig_d = shifted_center[0] + D_grid * new_z_re[0] + V_grid * new_y_re[0] + U_grid * new_x_re[0]
orig_v = shifted_center[1] + D_grid * new_z_re[1] + V_grid * new_y_re[1] + U_grid * new_x_re[1]
orig_u = shifted_center[2] + D_grid * new_z_re[2] + V_grid * new_y_re[2] + U_grid * new_x_re[2]

coords_new = [orig_d, orig_v, orig_u]

# Resample ref_volume using zero-padding (mode='constant', cval=0)
new_ref_volume = ndimage.map_coordinates(ref_volume, coords_new, order=1, mode='constant', cval=0)

# Determine the central slice index in new_ref_volume (expected to be at d=0)
central_slice_index = Lz_pixels // 2
print("New re-sliced 3D volume shape:", new_ref_volume.shape)
print("Central slice index in new volume:", central_slice_index)

# Determine the central slice index in the new volume (along d)
ref_slice_index = Lz_pixels // 2

import matplotlib.pyplot as plt

# Extract the central slice from the new 3D oblique volume.
central_slice = new_ref_volume[50, :, :]

# Display the central slice.
plt.figure(figsize=(10, 8))
plt.imshow(central_slice, cmap='inferno')
plt.title(f'Central Oblique Slice (index {central_slice_index})')
plt.axis('off')
plt.show()

def transform_modality_to_oblique_view(volume):
    """
    Transform a 3D volume to the final oblique view using two sequential transformations.
    
    Parameters:
    -----------
    volume : 3D numpy array
        Input volume to transform (e.g., data['T2'], data['T1'])
        
    Returns:
    --------
    new_ref_volume : 3D numpy array
        Transformed volume in final oblique view
    """
    import numpy as np
    from scipy import ndimage
    
    # ===== FIRST TRANSFORMATION (from cell 4) =====
    # Parameters for first transformation
    best_slice = 60
    Lu = 150
    Lv = 150
    Lz = 101
    D = 100
    fixed_offset = -7
    theta = -45
    
    # tranpose operation
    volume = np.transpose(volume, [2, 1, 0])
    # Get volume dimensions
    Z_in, Y_in, X_in = volume.shape
    
    # Define center in original volume coordinates
    center = np.array([best_slice, Y_in/2, X_in/2])
    
    # Define new coordinate axes
    theta_rad = np.deg2rad(theta)
    new_x = np.array([0, np.cos(theta_rad), np.sin(theta_rad)])
    new_y = np.array([1, 0, 0])
    new_z = -np.cross(new_x, new_y)
    
    # Incorporate fixed offset
    shifted_center = center + fixed_offset * new_z
    
    # Set up dimensions
    Lu_pixels = int(Lu)
    Lv_pixels = int(Lv)
    Lz_pixels = int(Lz)
    
    # Create grids for new coordinates
    u = np.linspace(-Lu/2, Lu/2, Lu_pixels)
    v = np.linspace(-Lv/2, Lv/2, Lv_pixels)
    d = np.linspace(-D/2, D/2, Lz_pixels)
    
    # Create meshgrid
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')
    
    # Map coordinates from new to original volume
    orig_Z = shifted_center[0] + D_grid * new_z[0] + V_grid * new_y[0] + U_grid * new_x[0]
    orig_Y = shifted_center[1] + D_grid * new_z[1] + V_grid * new_y[1] + U_grid * new_x[1]
    orig_X = shifted_center[2] + D_grid * new_z[2] + V_grid * new_y[2] + U_grid * new_x[2]
    
    coords = [orig_Z, orig_Y, orig_X]
    
    # Reslice the original volume (first transformation)
    ref_volume = ndimage.map_coordinates(volume, coords, order=1, mode='constant', cval=0)
    
    # ===== SECOND TRANSFORMATION (from cell 5) =====
    # Parameters for second transformation
    new_best_slice = 51
    Lu_new = 150
    Lv_new = 150
    Lz_new = 101
    D_new = 100
    fixed_offset = 0
    new_theta = -18
    
    # Dimensions of the intermediate volume
    Lz_old, Lv_old, Lu_old = ref_volume.shape
    
    # Define center in intermediate volume coordinates
    center_new = np.array([new_best_slice, Lv_old/2, Lu_old/2])
    
    # Define new coordinate axes
    new_theta_rad = np.deg2rad(new_theta)
    new_x_re = np.array([0, np.cos(new_theta_rad), np.sin(new_theta_rad)])
    new_y_re = np.array([1, 0, 0])
    new_z_re = -np.cross(new_x_re, new_y_re)
    
    # Incorporate fixed offset
    shifted_center = center_new + fixed_offset * new_z_re
    
    # Set up dimensions
    Lu_pixels = int(Lu_new)
    Lv_pixels = int(Lv_new)
    Lz_pixels = int(Lz_new)
    
    # Create grids for local coordinates
    u = np.linspace(-Lu_new/2, Lu_new/2, Lu_pixels)
    v = np.linspace(-Lv_new/2, Lv_new/2, Lv_pixels)
    d = np.linspace(-D_new/2, D_new/2, Lz_pixels)
    
    # Meshgrid
    D_grid, V_grid, U_grid = np.meshgrid(d, v, u, indexing='ij')
    
    # Map coordinates
    orig_d = shifted_center[0] + D_grid * new_z_re[0] + V_grid * new_y_re[0] + U_grid * new_x_re[0]
    orig_v = shifted_center[1] + D_grid * new_z_re[1] + V_grid * new_y_re[1] + U_grid * new_x_re[1]
    orig_u = shifted_center[2] + D_grid * new_z_re[2] + V_grid * new_y_re[2] + U_grid * new_x_re[2]
    
    coords_new = [orig_d, orig_v, orig_u]
    
    # Resample the intermediate volume (second transformation)
    new_ref_volume = ndimage.map_coordinates(ref_volume, coords_new, order=1, mode='constant', cval=0)
    
    return new_ref_volume

# Example usage:
# Transform different modalities into the same view
transformed_T2 = transform_modality_to_oblique_view(data['T2'])
transformed_T1 = transform_modality_to_oblique_view(data['T1'])
transformed_PD = transform_modality_to_oblique_view(data['PD'])
transformed_B0 = transform_modality_to_oblique_view(shimmed_data['B0'])
transformed_shimmedB0 = transform_modality_to_oblique_view(data['ShimmedB0'])
transformed_T2Star = transform_modality_to_oblique_view(data['T2star'])
transformed_T2Star_plus = transform_modality_to_oblique_view(data['T2star_plus'])
transformed_labels = transform_modality_to_oblique_view(data['Labels'])
# Verify the shapes of transformed volumes  
print("Transformed volumes shapes:")
print("T2 shape:", transformed_T2.shape)
print("T1 shape:", transformed_T1.shape)
print("PD shape:", transformed_PD.shape)
print("B0 shape:", transformed_B0.shape)
print("shimB0 shape:", transformed_shimmedB0.shape)
print("T2* shape:", transformed_T2Star.shape)
print("T2*+ shape:", transformed_T2Star_plus.shape)
print("Labels shape:", transformed_labels.shape)

def plot_transformed_modalities_with_swapped_axes(transformed_data):
    """
    Plot all transformed modalities with:
    1. Swapped x and y axes
    2. Y-axis flipped vertically (upside down)
    3. Modified colormaps for T2, T2*, and T2*+
    
    Parameters:
    -----------
    transformed_data : dict
        Dictionary containing all transformed modalities
    """
    import matplotlib.pyplot as plt
    import numpy as np
    import matplotlib.colors as mcolors
    
    # Create custom colormap from blue-purple to red-orange to yellow-white
    # This creates a colormap that matches your requirements
    from matplotlib.colors import LinearSegmentedColormap
    
    # Define custom colormap for T2 values
    colors = [(0.0, 'darkblue'),   # Dark blue for low values
              (0.2, 'purple'),      # Purple for low-mid values
              (0.3, 'red'),         # Red for mid values
              (0.4, 'orange'),      # Orange for mid-high values
              (1.0, 'yellow')]      # Yellow for high values
    
    # Create the custom colormap
    t2_cmap = LinearSegmentedColormap.from_list('t2_custom', colors)
    
    # Define parameter visualization settings with updated colormaps
    param_settings = {
        'PD': {'cmap': 'gray', 'title': 'Proton Density', 'vmin': None, 'vmax': None},
        'T1': {'cmap': 'hot', 'title': 'T1 (ms)', 'vmin': 0, 'vmax': 2000},
        'T2': {'cmap': 'inferno', 'title': 'T2 (ms)', 'vmin': 0, 'vmax': 150}, # Custom colormap
        'T2star': {'cmap': 'inferno', 'title': 'T2* (ms)', 'vmin': 0, 'vmax': 100}, # Custom colormap
        'T2star_plus': {'cmap': 'inferno', 'title': 'T2*+ (ms)', 'vmin': 0, 'vmax': 100}, # Custom colormap
        'B0': {'cmap': 'jet', 'title': 'B0 Field (Hz)', 'vmin': -100, 'vmax': 100},
        'ShimmedB0': {'cmap': 'jet', 'title': 'Shimmed B0 Field (Hz)', 'vmin': -100, 'vmax': 100},
        'Labels': {'cmap': 'tab20', 'title': 'Cardiac Labels', 'vmin': 0, 'vmax': None}
    }
    
    # Get all modalities and sort them
    modalities = list(transformed_data.keys())
    modalities.sort()  # Sort alphabetically
    
    # Calculate rows and columns for subplots
    n_modalities = len(modalities)
    n_cols = 3
    n_rows = (n_modalities + n_cols - 1) // n_cols
    
    # Create figure
    plt.figure(figsize=(15, 4 * n_rows))
    
    # Plot each modality
    for i, modality in enumerate(modalities):
        plt.subplot(n_rows, n_cols, i + 1)
        
        # Extract the central slice
        central_slice = transformed_data[modality][transformed_data[modality].shape[0] // 2, :, :]
        
        # 1. Transpose the slice (swap x and y)
        # 2. Flip vertically (upside down)
        central_slice = central_slice.T[::-1, :]
        
        # Get visualization settings
        settings = param_settings.get(modality, 
                                     {'cmap': 'viridis', 'title': modality, 'vmin': None, 'vmax': None})
        
        # Display the image
        im = plt.imshow(central_slice, 
                      cmap=settings['cmap'],
                      vmin=settings['vmin'],
                      vmax=settings['vmax'])
        
        plt.colorbar(im, fraction=0.046, pad=0.04)
        plt.title(f"{settings['title']}\n(min: {np.min(central_slice):.1f}, max: {np.max(central_slice):.1f})")
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

# Create a dictionary of transformed modalities
transformed_data = {
    'T1': transformed_T1,
    'T2': transformed_T2,
    'PD': transformed_PD,
    'B0': transformed_B0,
    'ShimmedB0': transformed_shimmedB0,
    'T2star': transformed_T2Star,
    'T2star_plus': transformed_T2Star_plus,
    'Labels': transformed_labels
}

# Plot all modalities with swapped axes and vertical flip
plot_transformed_modalities_with_swapped_axes(transformed_data)

import numpy as np

def reorganize_volume(volume):
    """
    Reorganize a 3D volume by:
    1. Keeping z-dimension (first dimension) unchanged
    2. Swapping x and y dimensions (transposing each slice)
    3. Flipping the new y-axis vertically
    
    Parameters:
    -----------
    volume : 3D numpy array with shape (z, y, x)
    
    Returns:
    --------
    reorganized : 3D numpy array with shape (z, x, y) and y-flipped
    """
    z_slices = volume.shape[0]
    reorganized = np.zeros((z_slices, volume.shape[2], volume.shape[1]))
    
    for z in range(z_slices):
        # Get the slice, transpose it, and flip y-axis
        reorganized[z, :, :] = volume[z, :, :].T[::-1, :]
    
    return reorganized

# Create a dictionary of reorganized transformed modalities
reorganized_data = {}

for key, volume in transformed_data.items():
    print(f"Reorganizing {key}...")
    reorganized_data[key] = reorganize_volume(volume)
    print(f"  Original shape: {volume.shape}, Reorganized shape: {reorganized_data[key].shape}")

print("\nReorganization complete!")

# Example: Show shape of original vs reorganized T2 volume
print(f"\nOriginal T2 shape: {transformed_data['T2'].shape}")
print(f"Reorganized T2 shape: {reorganized_data['T2'].shape}")

# Function to simulate MRI signals from tissue property maps
def simulate_mri_signal(pd_map, t1_map, t2_map, t2star_map, b0_map,TR, TE, flip_angle, sequence_type='spin_echo'):
    """
    Generate synthetic MRI images from tissue property maps
    
    Parameters:
    -----------
    pd_map, t1_map, t2_map, t2star_map : 2D arrays
        Tissue property maps
    TR : float
        Repetition time in ms
    TE : float
        Echo time in ms
    flip_angle : float
        Flip angle in degrees
    sequence_type : str
        'spin_echo', 'gradient_echo', or 'bssfp'
        
    Returns:
    --------
    image : 2D array
        Simulated MR image
    """
    # Convert flip angle to radians if provided in degrees
    if flip_angle > 6.28:  # If larger than 2π, assume it's in degrees
        flip_angle = np.deg2rad(flip_angle)
    
    # Create image containers
    signal = np.zeros_like(pd_map, dtype=complex)
    
    if sequence_type.lower() == 'spin_echo':
        # T2-weighted spin echo: S = PD * (1-exp(-TR/T1)) * exp(-TE/T2)
        t1_factor = 1 - np.exp(-TR / (t1_map + 1e-6))
        t2_factor = np.exp(-TE / (t2_map + 1e-6))
        signal = pd_map * t1_factor * t2_factor
        
    elif sequence_type.lower() == 'gradient_echo':
        # Gradient echo: S = PD * sin(α) * (1-exp(-TR/T1)) / (1-cos(α)*exp(-TR/T1)) * exp(-TE/T2*)
        t1_factor = np.sin(flip_angle) * (1 - np.exp(-TR / (t1_map + 1e-6)))
        denominator = 1 - np.cos(flip_angle) * np.exp(-TR / (t1_map + 1e-6))
        t2star_factor = np.exp(-TE / (t2star_map + 1e-6))
        signal = pd_map * (t1_factor / (denominator + 1e-6)) * t2star_factor
        # In the gradient_echo case:
        b0_phase = 2 * np.pi * b0_map * TE / 1000.0  # B0 in Hz, TE in ms
        signal = signal * np.exp(1j * b0_phase)  # Add B0-induced phase
        
    elif sequence_type.lower() == 'bssfp':
        # Simplified bSSFP signal equation
        t1_t2_ratio = t1_map / (t2_map + 1e-6)
        signal = pd_map * np.sin(flip_angle) / ((t1_t2_ratio + 1) - np.cos(flip_angle) * (t1_t2_ratio - 1)) * np.exp(-TE/(t2_map + 1e-6))
    
    # Add some realistic phase variation
    phase_noise = np.random.normal(0, 0.1, pd_map.shape)  # Adjust standard deviation as needed
    signal = signal * np.exp(1j * phase_noise)
    return signal  # Return magnitude image

# Get a slice from the 3D volume
# Get a slice from the reorganized 3D volumes
z_slice = reorganized_data['T2'].shape[0] // 2 - 0  # Z is still the first dimension
print('z_lice:', z_slice)
# Extract maps from reorganized data
pd_map = reorganized_data['PD'][z_slice,:,:]
t1_map = reorganized_data['T1'][z_slice,:,:]
t2_map = reorganized_data['T2'][z_slice,:,:]
t2star_map = reorganized_data['T2star'][z_slice,:,:]
t2star_plus_map = reorganized_data['T2star_plus'][z_slice,:,:]
b0_map = reorganized_data['B0'][z_slice,:,:]
shimb0_map = reorganized_data['ShimmedB0'][z_slice,:,:]
print(f'size of b0_map: {b0_map.shape}')

# Check if Labels exists in reorganized_data (might depend on your previous processing)
if 'Labels' in reorganized_data:
    labels = reorganized_data['Labels'][z_slice,:,:]
else:
    print("Warning: No 'Labels' key found in reorganized_data.")
    # Create a placeholder or use a different approach
    labels = np.ones_like(t2_map)  # Default placeholder


# Crop the image to remove the padding area

# Get current map dimensions
h, w = pd_map.shape
print(f"Original shape: {pd_map.shape}")

# Find combined non-zero regions to determine center of interest
combined_mask = (pd_map != 0) | (t1_map != 0) | (t2_map != 0) | \
                (t2star_map != 0) | (t2star_plus_map != 0) | (b0_map != 0)

# Find the center of the non-zero region
y_indices, x_indices = np.where(combined_mask)
center_y = int(np.mean(y_indices))
center_x = int(np.mean(x_indices))

# Calculate crop boundaries for 100x100 region centered on the region of interest
crop_size = 90
half_size = crop_size // 2

# Calculate boundaries ensuring they stay within image dimensions
row_min = max(0, center_y - half_size)
row_max = min(h - 1, center_y + half_size - 1)
col_min = max(0, center_x - half_size)
col_max = min(w - 1, center_x + half_size - 1)

# Adjust if crop region is smaller than desired size
if row_max - row_min + 1 < crop_size:
    if row_min == 0:
        row_max = min(h - 1, crop_size - 1)
    elif row_max == h - 1:
        row_min = max(0, h - crop_size)
        
if col_max - col_min + 1 < crop_size:
    if col_min == 0:
        col_max = min(w - 1, crop_size - 1)
    elif col_max == w - 1:
        col_min = max(0, w - crop_size)

# Crop all maps to the 100x100 region
pd_map_cropped = pd_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t1_map_cropped = t1_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t2_map_cropped = t2_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t2star_map_cropped = t2star_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
t2star_plus_map_cropped = t2star_plus_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
b0_map_cropped = b0_map[row_min:row_min+crop_size, col_min:col_min+crop_size]
shimb0_map_cropped = shimb0_map[row_min:row_min+crop_size, col_min:col_min+crop_size]


# Also crop the labels map if it exists
if 'labels' in locals():
    labels_cropped = labels[row_min:row_min+crop_size, col_min:col_min+crop_size]

# Print size information
print(f"Fixed cropped shape: {pd_map_cropped.shape}")
print(f"Cropping bounds: rows [{row_min}:{row_min+crop_size}], cols [{col_min}:{col_min+crop_size}]")
print(f"Removed {pd_map.size - pd_map_cropped.size} pixels ({(1 - pd_map_cropped.size/pd_map.size)*100:.1f}% reduction)")

# Visualize the cropped region on one of the maps
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.imshow(t2_map, cmap='viridis')
plt.title("Original T2 Map")
plt.axhline(y=row_min, color='r', linestyle='--')
plt.axhline(y=row_min+crop_size, color='r', linestyle='--')
plt.axvline(x=col_min, color='r', linestyle='--')
plt.axvline(x=col_min+crop_size, color='r', linestyle='--')
plt.colorbar()

plt.subplot(1, 2, 2)
plt.imshow(t2_map_cropped, cmap='viridis')
plt.title("100×100 Cropped T2 Map")
plt.colorbar()

plt.tight_layout()
plt.show()

# Replace the original maps with fixed-size cropped versions
pd_map = pd_map_cropped
t1_map = t1_map_cropped
t2_map = t2_map_cropped
t2star_map = t2star_map_cropped
t2star_plus_map = t2star_plus_map_cropped
b0_map = b0_map_cropped
shimb0_map = shimb0_map_cropped

if 'labels' in locals():
    labels = labels_cropped

print(f"New size of b0_map: {b0_map.shape}")
print(f"All maps are now exactly 100×100 pixels")


# Create mask for cardiac region
cardiac_mask = np.zeros_like(labels, dtype=bool)
for label in [1, 2, 5, 6]:  # LV wall, RV wall, LV blood, RV blood
    cardiac_mask |= (labels == label)

# Define sequence parameters
# Spin echo
tr_se = 700  # Long TR to reduce T1 effects
te_se = 1.33    # Long TE to enhance T2 contrast
fa_se = 90    # 90° flip angle for spin echo

# tr_se = 30  # Long TR to reduce T1 effects
# te_se = 10    # Long TE to enhance T2 contrast
# fa_se = 15    # 90° flip angle for spin echo

# Gradient echo
tr_gre = 30    # Shorter TR
te_gre = 25    # Medium TE - more sensitive to T2* effects
fa_gre = 8    # Small flip angle

# Balanced SSFP
tr_bssfp = 39.36    # Very short TR
te_bssfp = 1.44   # Very short TE
fa_bssfp = 15   # Higher flip angle

# tr_bssfp = 30    # Very short TR
# te_bssfp = 25   # Very short TE
# fa_bssfp = 15   # Higher flip angle

# Generate MR images
print("Simulating MR signals...")
spin_echo_complex = simulate_mri_signal(pd_map, t1_map, t2_map, t2star_plus_map, b0_map,tr_se, te_se, fa_se, 'spin_echo')
gre_t2star_complex = simulate_mri_signal(pd_map, t1_map, t2_map, t2star_map, b0_map, tr_gre, te_gre, fa_gre, 'gradient_echo')
gre_t2star_plus_complex = simulate_mri_signal(pd_map, t1_map, t2_map, t2star_plus_map, b0_map, tr_gre, te_gre, fa_gre, 'gradient_echo')
bssfp_complex= simulate_mri_signal(pd_map, t1_map, t2_map, t2star_plus_map, b0_map, tr_bssfp, te_bssfp, fa_bssfp, 'bssfp')

# Generate k-space data using FFT
spin_echo_kspace = np.fft.fftshift(np.fft.fft2(spin_echo_complex))
gre_t2star_kspace = np.fft.fftshift(np.fft.fft2(gre_t2star_complex))
gre_t2star_plus_kspace = np.fft.fftshift(np.fft.fft2(gre_t2star_plus_complex))
bssfp_kspace = np.fft.fftshift(np.fft.fft2(bssfp_complex))

spin_echo_image = np.abs(spin_echo_complex)
gre_t2star_image = np.abs(gre_t2star_complex)
gre_t2star_plus_image = np.abs(gre_t2star_plus_complex)
bssfp_image = np.abs(bssfp_complex)

# Calculate difference map between T2* and T2*+ images
difference_map = gre_t2star_plus_image - gre_t2star_image
percent_difference = 100 * np.abs(difference_map) / (gre_t2star_image + 1e-6)

# Visualization
plt.figure(figsize=(20, 16))

# Row 1: Parameter maps (T2*, T2*+, B0)
plt.subplot(4, 4, 1)
plt.imshow(t2star_map, cmap='jet', vmin=0, vmax=60)
plt.title('T2* Map (ms)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 2)
plt.imshow(t2star_plus_map, cmap='jet', vmin=0, vmax=60)
plt.title('T2*+ Map (ms)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 3)
plt.imshow(b0_map, cmap='coolwarm', vmin=-50, vmax=50)
plt.title('B0 Field (Hz)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 4)
plt.imshow(t2star_plus_map - t2star_map, cmap='coolwarm', vmin=-20, vmax=20)
plt.title('T2*+ - T2* Difference (ms)')
plt.colorbar()
plt.axis('off')

# Row 2: Simulated MR images
plt.subplot(4, 4, 5)
plt.imshow(spin_echo_image, cmap='gray')
plt.title(f'Spin Echo\nTR={tr_se}ms, TE={te_se}ms')
plt.axis('off')

plt.subplot(4, 4, 6)
plt.imshow(gre_t2star_image, cmap='gray')
plt.title(f'Gradient Echo (T2*)\nTR={tr_gre}ms, TE={te_gre}ms')
plt.axis('off')

plt.subplot(4, 4, 7)
plt.imshow(gre_t2star_plus_image, cmap='gray')
plt.title(f'Gradient Echo (T2*+)\nTR={tr_gre}ms, TE={te_gre}ms')
plt.axis('off')

plt.subplot(4, 4, 8)
plt.imshow(bssfp_image, cmap='gray')
plt.title(f'bSSFP\nTR={tr_bssfp}ms, TE={te_bssfp}ms')
plt.axis('off')

# Row 3: K-space representations (log scale for better visualization)
plt.subplot(4, 4, 9)
plt.imshow(np.log(np.abs(spin_echo_kspace) + 1e-10), cmap='gray')
plt.title('Spin Echo k-space')
plt.axis('off')

plt.subplot(4, 4, 10)
plt.imshow(np.log(np.abs(gre_t2star_kspace) + 1e-10), cmap='gray')
plt.title('Gradient Echo (T2*) k-space')
plt.axis('off')

plt.subplot(4, 4, 11)
plt.imshow(np.log(np.abs(gre_t2star_plus_kspace) + 1e-10), cmap='gray')
plt.title('Gradient Echo (T2*+) k-space')
plt.axis('off')

plt.subplot(4, 4, 12)
plt.imshow(np.log(np.abs(bssfp_kspace) + 1e-10), cmap='gray')
plt.title('bSSFP k-space')
plt.axis('off')

# Row 4: Focus on T2* vs T2*+ comparison
plt.subplot(4, 4, 13)
# Create cardiac region mask overlay
overlay = np.zeros((*cardiac_mask.shape, 3))
for label, color in [(1, [1, 0, 0]),     # LV wall - red
                     (2, [0, 0, 1]),     # RV wall - blue
                     (5, [1, 0.7, 0.7]), # LV blood - light red
                     (6, [0.7, 0.7, 1])]: # RV blood - light blue
    overlay[labels == label] = color
plt.imshow(overlay)
plt.title('Cardiac Region')
plt.axis('off')

plt.subplot(4, 4, 14)
plt.imshow(difference_map, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.title('GRE Signal Difference\n(T2*+ - T2*)')
plt.colorbar()
plt.axis('off')

plt.subplot(4, 4, 15)
# Apply cardiac mask to the percent difference
masked_percent_diff = percent_difference.copy()
masked_percent_diff[~cardiac_mask] = np.nan  # Make non-cardiac regions transparent
plt.imshow(masked_percent_diff, cmap='hot', vmin=0, vmax=20)
plt.title('% Difference in Cardiac Region')
plt.colorbar(label='%')
plt.axis('off')

plt.subplot(4, 4, 16)
# Zoomed view of the difference in cardiac region
plt.imshow(difference_map * cardiac_mask, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.title('Cardiac Signal Difference\n(T2*+ - T2*)')
plt.colorbar()
plt.axis('off')

plt.tight_layout()
plt.show()

# Show a detailed comparison of T2* vs T2*+ in gradient echo
plt.figure(figsize=(18, 6))

# Find cardiac center for zooming
if np.any(cardiac_mask):
    y_indices, x_indices = np.where(cardiac_mask)
    center_y, center_x = int(np.mean(y_indices)), int(np.mean(x_indices))
else:
    center_y, center_x = labels.shape[0] // 2, labels.shape[1] // 2

# Define zoom region
zoom_size = 50
zoom_y_min = max(0, center_y - zoom_size)
zoom_y_max = min(labels.shape[0], center_y + zoom_size)
zoom_x_min = max(0, center_x - zoom_size)
zoom_x_max = min(labels.shape[1], center_x + zoom_size)

# Original GRE images
plt.subplot(2, 3, 1)
plt.imshow(gre_t2star_image, cmap='gray')
plt.title('GRE with T2*')
plt.gca().add_patch(plt.Rectangle((zoom_x_min, zoom_y_min), 
                                 zoom_x_max-zoom_x_min, zoom_y_max-zoom_y_min,
                                 edgecolor='r', facecolor='none', linewidth=2))
plt.axis('off')

plt.subplot(2, 3, 2)
plt.imshow(gre_t2star_plus_image, cmap='gray')
plt.title('GRE with T2*+\n(includes B0 effects)')
plt.gca().add_patch(plt.Rectangle((zoom_x_min, zoom_y_min), 
                                 zoom_x_max-zoom_x_min, zoom_y_max-zoom_y_min,
                                 edgecolor='r', facecolor='none', linewidth=2))
plt.axis('off')

plt.subplot(2, 3, 3)
plt.imshow(difference_map, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.title('Difference (T2*+ - T2*)')
plt.gca().add_patch(plt.Rectangle((zoom_x_min, zoom_y_min), 
                                 zoom_x_max-zoom_x_min, zoom_y_max-zoom_y_min,
                                 edgecolor='r', facecolor='none', linewidth=2))
plt.colorbar()
plt.axis('off')

# Zoomed versions
plt.subplot(2, 3, 4)
plt.imshow(gre_t2star_image[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title('Zoomed GRE with T2*')
plt.axis('off')

plt.subplot(2, 3, 5)
plt.imshow(gre_t2star_plus_image[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title('Zoomed GRE with T2*+')
plt.axis('off')

plt.subplot(2, 3, 6)
plt.imshow(difference_map[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.title('Zoomed Difference')
plt.colorbar()
plt.axis('off')

plt.tight_layout()
plt.show()

import numpy as np
import matplotlib.pyplot as plt

def qdf(a, b, c):
    """
    Solves quadratic equation ax^2 + bx + c = 0
    
    Returns:
        Roots of the quadratic equation
    """
    d = b**2 - 4*a*c
    if d < 0:
        # Handle negative discriminant case
        d = complex(d, 0)
    root1 = (-b + np.sqrt(d)) / (2*a)
    root2 = (-b - np.sqrt(d)) / (2*a)
    return np.array([root1, root2])

def findq2r2(smax, gmax, r, r1, T, Ts, N, Fcoeff, rmax, z=0):
    """
    Calculates the second derivatives of r and theta (q) that satisfy hardware and FOV constraints
    
    Parameters:
        smax: Maximum slew rate in G/cm/s
        gmax: Maximum gradient amplitude in G/cm
        r: Current value of the k-space radius
        r1: Current derivative of r
        T: Gradient sample period
        Ts: Data sampling period
        N: Number of spiral interleaves
        Fcoeff: FOV coefficients for variable density
        rmax: Maximum k-space radius
        z: R/L for gradient coil (voltage model parameter)
    
    Returns:
        q2: Second derivative of angle theta
        r2: Second derivative of radius r
    """
    gamma = 4258  # Hz/G

    smax = smax + z*gmax

    # # Calculate FOV and its derivative for current r
    F = 0
    dFdr = 0
    for rind in range(len(Fcoeff)):
        F += Fcoeff[rind] * (r/rmax)**(rind)
        if rind > 0:
            dFdr += rind * Fcoeff[rind] * (r/rmax)**(rind-1) / rmax

    # # --- VARIABLE DENSITY IMPLEMENTATION ---
    # # Calculate oversampling factor: 8.0 at center, linearly decreasing to 1.0 at kmax
    # if rmax > 0:
    #     relative_r = min(r / rmax, 1.0)  # Clip to [0, 1]
    # else:
    #     relative_r = 0.0
        
    # # Linear oversampling profile: OS(r) = 8.0 - 7.0 * (r/rmax)
    # # OS_r = 8.0 - 7.0 * relative_r
    # OS_r = 7.0 * relative_r
    # OS_r = max(1.0, OS_r)  # Ensure minimum 1.0 oversampling
    # print(f'oversampling ratio: {OS_r}')
    
    # # Base FOV from Fcoeff[0]
    # F0 = Fcoeff[0]
    
    # # Calculate effective FOV and its derivative
    # F = F0 / OS_r
    
    # # Calculate derivative: d/dr[F0/(8-7r/rmax)]
    # if rmax > 0 and r < rmax:
    #     dFdr = 7.0 * F0 / (rmax * OS_r**2)
    # else:
    #     dFdr = 0.0

    # FOV limit on gradient
    GmaxFOV = N/gamma / F / Ts
    if not hasattr(findq2r2, "printed"):
        print(f'Required GmaxFOV: {GmaxFOV}')
        findq2r2.printed = True
    Gmax = min(GmaxFOV, gmax)
    # Gmax = gmax

    # Maximum allowed r1 based on gradient amplitude limit
    maxr1 = np.sqrt((gamma*Gmax)**2 / (1 + (2*np.pi*F*r/N)**2))

    if r1 > maxr1:
        # Gradient amplitude limited case
        r2 = (maxr1 - r1) / T
    else:
        # Slew rate limited case
        twopiFoN = 2*np.pi*F/N
        twopiFoN2 = twopiFoN**2

        # Coefficients for the quadratic equation in r2
        A = 1 + twopiFoN2*r*r
        B = 2*twopiFoN2*r*r1*r1 + 2*twopiFoN2/F*dFdr*r*r*r1*r1 + 2*z*r1 + 2*twopiFoN2*r1*r
        C1 = twopiFoN2**2*r*r*r1**4 + 4*twopiFoN2*r1**4 + (2*np.pi/N*dFdr)**2*r*r*r1**4 + 4*twopiFoN2/F*dFdr*r*r1**4 - (gamma)**2*smax**2
        C2 = z*(z*r1**2 + z*twopiFoN2*r1**2 + 2*twopiFoN2*r1**3*r + 2*twopiFoN2/F*dFdr*r1**3*r)
        C = C1 + C2

        # Solve quadratic equation
        rts = qdf(A, B, C)
        r2 = np.real(rts[0])  # Use first root

        # Calculate and check resulting slew rate
        slew = 1/gamma * (r2 - twopiFoN2*r*r1**2 + 1j*twopiFoN*(2*r1**2 + r*r2 + dFdr/F*r*r1**2))
        sr = np.abs(slew)/smax

        if sr > 1.01:
            print(f"Slew violation, slew = {round(np.abs(slew))}, smax = {round(smax)}, sr={sr:.3f}, r={r:.3f}, r1={r1:.3f}")

    # Calculate q2 from other parameters
    q2 = 2*np.pi/N*dFdr*r1**2 + 2*np.pi*F/N*r2
    
    return q2, r2

def vds(smax, gmax, T, N, Fcoeff, rmax, z=0):
    """
    Variable Density Spiral trajectory generation
    
    Parameters:
        smax: Maximum slew rate G/cm/s
        gmax: Maximum gradient G/cm
        T: Sampling period (s)
        N: Number of interleaves
        Fcoeff: FOV coefficients - FOV(r) = Sum_k Fcoeff[k]*(r/rmax)^k
        rmax: Maximum k-space radius (cm^-1)
        z: R/L for gradient coil model
        
    Returns:
        k: k-space trajectory (kx+iky) in cm^-1
        g: gradient waveform (Gx+iGy) in G/cm
        s: derivative of g (Sx+iSy) in G/cm/s
        time: time points corresponding to trajectory (s)
        r: k-space radius vs time
        theta: angle vs time
    """
    print('Variable Density Spiral Generation')
    gamma = 4258  # Hz/G

    # Oversampling for trajectory calculation
    oversamp = 8  # Keep this even
    To = T / oversamp  # Oversampled period

    # Initialize variables
    q0 = 0
    q1 = 0
    r0 = 0
    r1 = 0
    t = 0
    count = 0

    # Pre-allocate arrays (can extend later if needed)
    max_points = 10000000
    theta = np.zeros(max_points)
    r = np.zeros(max_points)
    time = np.zeros(max_points)

    # Main loop to generate trajectory
    while r0 < rmax:
        # Get the next point on the trajectory
        q2, r2 = findq2r2(smax, gmax, r0, r1, To, T, N, Fcoeff, rmax, z)

        # Integrate for θ, θ', r, and r'
        q1 = q1 + q2 * To
        q0 = q0 + q1 * To
        t = t + To

        r1 = r1 + r2 * To
        r0 = r0 + r1 * To

        # Store values
        count += 1
        theta[count] = q0
        r[count] = r0
        time[count] = t

        if count % 10000 == 0:
            print(f'{count} points, |k|={r0:.6f}')

        # Break if we've reached array limit
        if count >= max_points - 1:
            print("Warning: reached maximum array size")
            break

    # Trim arrays to used size
    theta = theta[:count+1]
    r = r[:count+1]
    time = time[:count+1]

    # Downsample to original sampling rate
    theta_ds = theta[oversamp//2::oversamp]
    r_ds = r[oversamp//2::oversamp]
    time_ds = time[oversamp//2::oversamp]

    # Keep the length a multiple of 4 (to match original code)
    length = 4 * (len(theta_ds) // 4)
    theta_ds = theta_ds[:length]
    r_ds = r_ds[:length]
    time_ds = time_ds[:length]

    # Calculate k-space trajectory, gradients, and slew rates
    k = r_ds * np.exp(1j * theta_ds)
    
    # Calculate gradients
    g = np.zeros_like(k, dtype=complex)
    g[:-1] = (k[1:] - k[:-1]) / T / gamma
    g[-1] = g[-2]  # Extrapolate last point
    
    # Calculate slew rates
    s = np.zeros_like(g, dtype=complex)
    s[:-1] = (g[1:] - g[:-1]) / T
    s[-1] = s[-2]  # Extrapolate last point

    # Plot results
    plot_vds_results(time_ds, k, g, s)

    return k, g, s, time_ds, r_ds, theta_ds

def plot_vds_results(time, k, g, s):
    """
    Plot the results of the VDS trajectory generation
    """
    # Undersample for plotting
    tp = time[::10]
    kp = k[::10]
    gp = g[::10]
    sp = s[::10]
    
    plt.figure(figsize=(12, 10))
    
    # Plot 1: k-space trajectory
    plt.subplot(2, 2, 1)
    plt.plot(np.real(kp), np.imag(kp))
    plt.title('ky vs kx')
    plt.xlabel('kx (cm$^{-1}$)')
    plt.ylabel('ky (cm$^{-1}$)')
    plt.axis('square')
    plt.grid(True)
    
    # Plot 2: k-space vs time
    plt.subplot(2, 2, 2)
    plt.plot(tp, np.real(kp), 'c-', label='kx')
    plt.plot(tp, np.imag(kp), 'g-', label='ky')
    plt.title('k-space vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('k (cm$^{-1}$)')
    plt.legend()
    plt.grid(True)
    
    # Plot 3: Gradient vs time
    plt.subplot(2, 2, 3)
    plt.plot(tp, np.real(gp), 'c-', label='Gx')
    plt.plot(tp, np.imag(gp), 'g-', label='Gy')
    plt.title('Gradient vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('g (G/cm)')
    plt.legend()
    plt.grid(True)
    
    # Plot 4: Slew rate vs time
    plt.subplot(2, 2, 4)
    plt.plot(tp, np.real(sp), 'c-', label='Sx')
    plt.plot(tp, np.imag(sp), 'g-', label='Sy')
    plt.plot(tp, np.abs(sp), 'k-', label='|S|')
    plt.title('Slew-Rate vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('s (G/cm/s)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

# Example parameters
smax = 20000       # Maximum slew rate (G/cm/s) 200T/m/s
gmax = 6           # Maximum gradient amplitude (G/cm) 40 mt/m
T = 4e-6           # Sampling period (s)
N = 1     # Number of interleaves
# Fcoeff = [192, -168]   # FOV = 18 cm (constant)或者等效地 [19.2, -16.8] (cm单位)
Fcoeff = [18, 0]
sf = 1
rmax = sf * 1/(2*0.2)   # Maximum k-space radius for 2mm resolution

# Example with variable density
# FOV = 24 - 4*(r/rmax) cm (decreases from 24cm to 20cm)
# Fcoeff = [24, -4]


# Generate the spiral trajectory
k, g, s, time, r, theta = vds(smax, gmax, T, N, Fcoeff, rmax)

print(f'matrix size of kspace: {k.shape}')
print(f'maximum radial distance from kspace center: {r.max()}')
print(f'gradient magnitude maximum value: {abs(g.max())}')
print(f'slew rate maximum value: {abs(s.max())}')
print(f'time shape: {time.shape}')
print(f'minimum read out time: {time[-1]*1000} ms')

# Extract kx and ky components from complex k-space trajectory
kx = np.real(k)  # Real part of k (x-component)
ky = np.imag(k)  # Imaginary part of k (y-component)
delta_kx = kx[1] - kx[0]
print(f'delta kx: {delta_kx}')
delta_r = r[1] - r[0]
print(f'delta r: {delta_r}')
# Get maximum k-space radius
k_max = r.max() /sf # Using the r variable already calculated in vds function

print(f"kx min: {np.min(kx)}, kx max: {np.max(kx)}")
print(f"ky min: {np.min(ky)}, ky max: {np.max(ky)}")
print(f"max r: {np.max(np.sqrt(kx**2 + ky**2))}")

# import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata
import cv2
import numpy as np
import matplotlib.pyplot as plt

# Use gre_t2star_plus_image as input
print(f"Using GRE T2*+ image for spiral simulation, shape: {gre_t2star_complex.shape}")

# Create a correctly sized square image
def resize_to_square(image, target_size=500):
    """Resizes an image to a square without distortion"""
    h, w = image.shape
    
    # If already the right size
    if h == target_size and w == target_size:
        return image
        
    # If different dimensions, crop or pad
    if h != w or h != target_size:
        # Create empty square canvas
        result = np.zeros((target_size, target_size))
        
        # Scale down if needed
        if h > target_size or w > target_size:
            scale = target_size / max(h, w)
            new_h, new_w = int(h * scale), int(w * scale)
            from scipy.ndimage import zoom
            resized = zoom(image, (new_h/h, new_w/w))
            h, w = new_h, new_w
        else:
            resized = image
            
        # Center image in canvas
        y_offset = (target_size - h) // 2
        x_offset = (target_size - w) // 2
        
        # Place image in center
        result[y_offset:y_offset+h, x_offset:x_offset+w] = resized
        return result
    
    return image

# Resize the T2*+ GRE image
image_size = 90  # Adjusted to better match the likely size of the GRE image
image_data = resize_to_square(gre_t2star_image, target_size=image_size)
# Verify dimensions
print(f"Resized image shape: {image_data.shape}")

# No need to load B0 map as it's already available from previous cell
# print(f"Using B0 map from previous cell, center slice")

# Convert image to k-space
# kspace = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(image_data)))
kspace = gre_t2star_kspace
print(f"Using k-space data for spiral simulation, shape: {kspace.shape}")



# import numpy as np
# import matplotlib.pyplot as plt
# from scipy.interpolate import griddata, NearestNDInterpolator
# import cv2
# from scipy.ndimage import gaussian_filter

# kx_spiral = kx
# ky_spiral = ky

# # --- ORIGINAL CODE START ---
# # Convert to INTEGER coordinates directly
# kx_pixel = ((kx_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
# ky_pixel = ((ky_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
# # --- ORIGINAL CODE END ---

# # Visualize the spiral trajectory properly (using integer coordinates for plot clarity)
# plt.figure(figsize=(15, 5))
# plt.subplot(1, 3, 1)
# plt.plot(kx_spiral, ky_spiral, 'b.', markersize=1)
# plt.title("k-space Spiral Trajectory (cycles/m)")
# plt.axis('equal')
# plt.grid(True)
# theta_vis = np.linspace(0, 2*np.pi, 100) # Define theta if not already defined
# plt.plot(k_max*np.cos(theta_vis), k_max*np.sin(theta_vis), 'r--')

# plt.subplot(1, 3, 2)
# # Use integer coordinates for pixel plot
# plt.plot(kx_pixel-image_size/2, ky_pixel-image_size/2, 'b.', markersize=1)
# plt.title("Spiral Trajectory (pixel coordinates)")
# plt.axis('equal')
# plt.grid(True)
# plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'r--')

# plt.subplot(1, 3, 3)
# # Use integer coordinates for pixel plot
# plt.plot(kx_pixel-image_size/2, ky_pixel-image_size/2, 'b.', markersize=1)
# plt.title("Spiral Trajectory (pixel coordinates)")
# plt.axis('equal')
# plt.grid(True)
# plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'r--')

# plt.tight_layout()
# plt.show()

# # Clip INTEGER coordinates to valid image coordinates
# kx_pixel = np.clip(kx_pixel, 0, image_size - 1)
# ky_pixel = np.clip(ky_pixel, 0, image_size - 1)

# # Interpolate k-space data onto spiral trajectory
# # Prepare grid points for interpolation
# kx_cart, ky_cart = np.meshgrid(np.arange(image_size), np.arange(image_size))
# kx_cart_flat = kx_cart.flatten()
# ky_cart_flat = ky_cart.flatten()
# print('kx_cart_flat size ', kx_cart_flat.size)
# kspace_flat = kspace.flatten()
# print(f"kspace shape: {kspace_flat.shape}")

# # Interpolate with nearest neighbor first (more robust)
# interp_nearest = NearestNDInterpolator(
#     np.column_stack((ky_cart_flat, kx_cart_flat)),
#     kspace_flat
# )
# # --- ORIGINAL CODE START ---
# # Use INTEGER coordinates for interpolation query
# spiral_kspace = interp_nearest(np.column_stack((ky_pixel, kx_pixel)))
# # --- ORIGINAL CODE END ---
# print(f'length of spiral: {spiral_kspace.size}')

# # Use a simple radial density compensation
# # --- ORIGINAL CODE START ---
# # Use INTEGER coordinates for radius calculation
# kx_rel = kx_pixel - image_size/2
# ky_rel = ky_pixel - image_size/2
# radius = np.sqrt(kx_rel**2 + ky_rel**2)
# max_radius = image_size/2
# # Add small offset to avoid division by zero or issues at k=0
# density_comp = np.sqrt(radius / max_radius + 0.1) # Original offset was 0.1
# # --- ORIGINAL CODE END ---
# spiral_kspace *= density_comp

# # Create a sparse k-space representation for visualization
# sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
# # --- ORIGINAL CODE START ---
# # Use INTEGER coordinates for indexing the sparse array
# # Direct assignment (overwrites if multiple samples map to the same pixel)
# sparse_kspace[ky_pixel, kx_pixel] = spiral_kspace
# # --- ORIGINAL CODE END ---

# # Apply a small blur to make the spiral pattern more visible in the visualization
# blurred_viz = gaussian_filter(np.abs(sparse_kspace), sigma=1.5)

# coverage_mask = (sparse_kspace != 0).astype(float)

# # --- ORIGINAL CODE START ---
# # This line uses the original kspace masked by where the rounded spiral landed.
# spiral_grid_kspace = kspace * coverage_mask
# # Reconstruction using the masked original k-space
# reconstructed_image = np.fft.ifft2(np.fft.ifftshift(sparse_kspace))
# # --- ORIGINAL CODE END ---

# # Display k-space results (raw and blurred for better visibility)
# plt.figure(figsize=(15, 5))
# plt.subplot(1, 3, 1)
# plt.imshow(np.log(np.abs(kspace) + 1e-10), cmap='gray')
# plt.title("Original k-space (Cartesian)")
# plt.colorbar()

# plt.subplot(1, 3, 2)
# plt.imshow(np.log(np.abs(sparse_kspace) + 1e-10), cmap='gray')
# plt.title("Spiral Sampled k-space (DCF Applied)")
# plt.colorbar()

# plt.subplot(1, 3, 3)
# plt.imshow(np.log(blurred_viz + 1e-10), cmap='gray')
# plt.title("Spiral Sampled k-space (Blurred for Visibility)")
# plt.colorbar()
# plt.tight_layout()
# plt.show()

# reconstructed_magnitude = np.abs(reconstructed_image)

# # Display original image vs reconstruction
# plt.figure(figsize=(12, 5))
# plt.subplot(1, 2, 1)
# plt.imshow(image_data, cmap='gray')
# plt.title("Original Image")
# plt.colorbar()

# plt.subplot(1, 2, 2)
# plt.imshow(reconstructed_magnitude, cmap='gray')
# plt.title("Reconstructed from Masked Cartesian k-space") # Title updated
# plt.colorbar()
# plt.tight_layout()
# plt.show()

# # Calculate k-space coverage
# sampled_pixels = np.count_nonzero(sparse_kspace)
# total_pixels = image_size * image_size
# coverage_percentage = (sampled_pixels / total_pixels) * 100

# # Create coverage visualization
# coverage_mask = (sparse_kspace != 0).astype(float)

# # Print coverage metric
# print(f"K-space Coverage Analysis:")
# print(f"  Total k-space pixels: {total_pixels}")
# print(f"  Sampled pixels: {sampled_pixels}")
# print(f"  Coverage percentage: {coverage_percentage:.2f}%")

# # Add coverage visualization to the figure
# plt.figure(figsize=(15, 5))
# plt.subplot(1, 3, 1)
# plt.imshow(np.log(np.abs(kspace) + 1e-10), cmap='gray')
# plt.title("Original k-space (Cartesian)")
# plt.colorbar()

# plt.subplot(1, 3, 2)
# plt.imshow(np.log(np.abs(sparse_kspace) + 1e-10), cmap='gray')
# plt.title(f"Spiral Sampled k-space\nCoverage: {coverage_percentage:.2f}%")
# plt.colorbar()

# plt.subplot(1, 3, 3)
# plt.imshow(coverage_mask, cmap='binary', vmin=0, vmax=1)
# plt.title("K-space Coverage Map")
# plt.colorbar(label='Sampled')
# plt.tight_layout()
# plt.show()

# import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import griddata, NearestNDInterpolator
import cv2
from scipy.ndimage import gaussian_filter
# import sigpy as sp
# import sigpy.mri as spmri
from scipy import special
import scipy.sparse as spr
from scipy.sparse.linalg import lsqr

kx_spiral = kx
ky_spiral = ky

# --- MODIFICATION START ---
# Calculate FLOATING POINT pixel coordinates first
kx_pixel_float = ((kx_spiral / k_max) * (image_size/2) + image_size/2)
ky_pixel_float = (ky_spiral / k_max) * (image_size/2) + image_size/2

# The range of ky_pixel_float
ky_pixel_float_range = (np.min(ky_pixel_float), np.max(ky_pixel_float))
print(f'inital range of ky_pixel float: {ky_pixel_float_range}')
kx_pixel_float_range = (np.min(kx_pixel_float), np.max(kx_pixel_float))
print(f'inital range of kx_pixel float: {kx_pixel_float_range}')


# Convert to INTEGER coordinates for visualization and sparse grid indexing later
kx_pixel_int = ((kx_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
ky_pixel_int = ((ky_spiral / k_max) * (image_size/2) + image_size/2).astype(int)
# --- MODIFICATION END ---

# Visualize the spiral trajectory properly (using integer coordinates for plot clarity)
plt.figure(figsize=(15, 5))
plt.subplot(1, 3, 1)
plt.plot(kx_spiral, ky_spiral, 'b.', markersize=1)
plt.title("k-space Spiral Trajectory (cycles/m)")
plt.axis('equal')
plt.grid(True)
theta_vis = np.linspace(0, 2*np.pi, 100) # Define theta if not already defined
plt.plot(k_max*np.cos(theta_vis), k_max*np.sin(theta_vis), 'r--')

plt.subplot(1, 3, 2)
# Use float coordinates for pixel plot
plt.plot(kx_pixel_float-image_size/2, ky_pixel_float-image_size/2, 'b.', markersize=1)
plt.title("Spiral Trajectory (pixel coordinates)")
plt.axis('equal')
plt.grid(True)
plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'r--')

plt.subplot(1, 3, 3)
# Use integer coordinates for pixel plot
plt.plot(kx_pixel_int-image_size/2, ky_pixel_int-image_size/2, 'b.', markersize=1)
plt.title("Spiral Trajectory (pixel coordinates)")
plt.axis('equal')
plt.grid(True)
plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'r--')

plt.tight_layout()
plt.show()

plt.figure(figsize=(7, 7))
plt.plot(kx_pixel_float - image_size/2, ky_pixel_float - image_size/2, 'b.', markersize=2, label='Float coords')
plt.plot(kx_pixel_int - image_size/2, ky_pixel_int - image_size/2, 'ro', markersize=2, label='Int coords', alpha=0.5)
plt.title("Spiral Trajectory: Float vs Int Pixel Coordinates")
plt.axis('equal')
plt.grid(True)
plt.plot((image_size/2)*np.cos(theta_vis), (image_size/2)*np.sin(theta_vis), 'k--', alpha=0.3)
plt.legend()
plt.tight_layout()
plt.show()

# Clip coordinates to valid image coordinates
kx_pixel_float = np.clip(kx_pixel_float, 0, image_size - 1)
ky_pixel_float = np.clip(ky_pixel_float, 0, image_size - 1)

# The range of ky_pixel_float
ky_pixel_float_range = (np.min(ky_pixel_float), np.max(ky_pixel_float))
print(f'clip range of ky_pixel float: {ky_pixel_float_range}')
kx_pixel_float_range = (np.min(kx_pixel_float), np.max(kx_pixel_float))
print(f'clip range of kx_pixel float: {kx_pixel_float_range}')


kx_pixel_int = np.clip(kx_pixel_int, 0, image_size - 1)
ky_pixel_int = np.clip(ky_pixel_int, 0, image_size - 1)

# Prepare grid points for interpolation
kx_cart, ky_cart = np.meshgrid(np.arange(image_size), np.arange(image_size))
kx_cart_flat = kx_cart.flatten()
ky_cart_flat = ky_cart.flatten()
print('kx_cart_flat size ', kx_cart_flat.size)
kspace_flat = kspace.flatten()
print(f"kspace shape: {kspace_flat.shape}")

# --- MODIFIED INTERPOLATION: KERNEL-BASED APPROACH ---
# Define a Kaiser-Bessel kernel function for interpolation
def kaiser_bessel_kernel(distance, width=2.0, beta=4.0):
    """
    Kaiser-Bessel kernel function for gridding/interpolation
    
    Parameters:
    -----------
    distance : float
        Normalized distance from the sample point (0 to width)
    width : float
        Width of the kernel (typically 2.0-3.0)
    beta : float
        Shape parameter (typically 4.0-13.0)
    
    Returns:
    --------
    kernel_value : float
        Kernel weight
    """
    if distance >= width:
        return 0.0
    
    x = (2.0 * distance / width)
    if x > 1.0:
        return 0.0
    
    # Note: Need to handle potential numerical issues
    if x < 1e-10:
        return 1.0
    
    y = beta * np.sqrt(1.0 - x*x)
    return np.i0(y) / np.i0(beta)  # i0 is modified Bessel function of first kind

def gaussian_kernel(distance, width=2.0, sigma=0.5):
    sigma = 0.5
    if distance >= width:
        return 0.0
    return np.exp(-0.5 * (distance/(sigma*width))**2)

def triangle_kernel(distance, width=2.0):
    if distance >= width:
        return 0.0
    return 1.0 - distance/width

def cubic_bspline_kernel(distance, width=2.0):
    distance = np.abs(distance)
    if distance >= 2:
        return 0.0
    elif distance < 1:
        return 2/3 - distance**2 + 0.5*distance**3
    else:  # 1 <= distance < 2
        return (2-distance)**3 / 6
    
def sinc_kernel(distance, width=4.0):
    if distance >= width:
        return 0.0
    if distance < 1e-10:
        return 1.0
    x = np.pi * distance
    return np.sin(x) / x

def pswf_kernel(distance, width=2.0, beta=3.0):
    if distance >= width:
        return 0.0
    x = (2.0 * distance / width) 
    return special.eval_legendre(0, beta * np.sqrt(1.0 - x*x)) / special.eval_legendre(0, beta)

# Apply kernel-based interpolation to get data at spiral points
spiral_kspace = np.zeros(len(kx_pixel_float), dtype=complex)
kernel_width_pixels = 1.0  # Kernel width in pixels
kernel_beta = 13.0  # Kernel shape parameter
weight_map = []  # 每个spiral点的权重分布
print("Performing kernel-based interpolation from Cartesian to spiral points...")
for i in range(len(kx_pixel_float)):
    x_center = kx_pixel_float[i]
    y_center = ky_pixel_float[i]
    local_weights = []
    x_min = max(0, int(x_center - kernel_width_pixels))
    x_max = min(image_size-1, int(x_center + kernel_width_pixels))
    y_min = max(0, int(y_center - kernel_width_pixels))
    y_max = min(image_size-1, int(y_center + kernel_width_pixels))
    weighted_sum = 0.0j
    total_weight = 0.0
    for y in range(y_min, y_max+1):
        for x in range(x_min, x_max+1):
            distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
            weight = kaiser_bessel_kernel(distance / kernel_width_pixels, width=1.1, beta=kernel_beta)
            # weight = gaussian_kernel(distance, width=0.5, sigma=0.5)
            # weight = triangle_kernel(distance / kernel_width_pixels)
            # weight = cubic_bspline_kernel(distance / kernel_width_pixels)
            # weight = sinc_kernel(distance / kernel_width_pixels)
            # weight = pswf_kernel(distance / kernel_width_pixels, width=0.5, beta=kernel_beta)
            if weight > 0 and kspace[y, x] != 0:
                weighted_sum += weight * kspace[y, x]
                total_weight += weight
                local_weights.append((y, x, weight))
    weight_map.append(local_weights)
    # spiral_kspace[i] = weighted_sum  # 不归一化
    if total_weight > 0:
        spiral_kspace[i] = weighted_sum / total_weight
    else:
        spiral_kspace[i] = 0.0
    

print(f'length of spiral: {spiral_kspace.size}')

# Use a simple radial density compensation
# Use FLOATING POINT coordinates for more accurate radius calculation
kx_rel = kx_pixel_float - image_size/2
ky_rel = ky_pixel_float - image_size/2
radius = np.sqrt(kx_rel**2 + ky_rel**2)
max_radius = image_size/2
# Add small epsilon to avoid division by zero or issues at k=0
# density_comp = np.sqrt(radius / max_radius + 1e-6)
# spiral_kspace *= density_comp
# coord = np.stack([kx_spiral / k_max * 0.5, ky_spiral / k_max * 0.5], axis=-1)  # 归一化到[-0.5,0.5]
# dcf = spmri.pipe_menon_dcf(coord, (image_size, image_size))
# dcf = np.maximum(dcf, 0.1)    # 最低都补偿 10%
# spiral_kspace *= dcf
# plt.plot(density_comp)
# plt.title('Density Compensation Function')
# plt.show()

# # Create a sparse k-space representation using kernel-based gridding
# sparse_kspace = np.zeros((image_size, image_size), dtype=complex)

# # Apply kernel-based gridding from spiral to Cartesian grid
print("Performing kernel-based gridding from spiral to Cartesian grid...")
# Track contribution weights to normalize properly
weight_grid = np.zeros((image_size, image_size), dtype=float)

# for i, local_weights in enumerate(weight_map):
#     for y, x, weight in local_weights:
#         sparse_kspace[y, x] += weight * spiral_kspace[i]
#         weight_grid[y, x] += weight

# Normalize by sum of weights to avoid density bias
mask = weight_grid > 0
# sparse_kspace[mask] /= weight_grid[mask]

# 假设 weight_map, spiral_kspace, image_size 已经计算好
n_samples = len(spiral_kspace)
n_pixels  = image_size * image_size

rows, cols, vals = [], [], []
b = np.zeros(n_samples, dtype=complex)

for i, local in enumerate(weight_map):
    W = sum(w for (_,_,w) in local)
    b[i] = spiral_kspace[i] * W
    for y, x, w in local:
        idx = y * image_size + x
        rows.append(i); cols.append(idx); vals.append(w)

A = spr.csr_matrix((vals, (rows, cols)), shape=(n_samples, n_pixels))

# 正则化参数 λ 防止病态，可调
λ = 1e-6
# lsqr 支持 damp 参数即 λ
sol = lsqr(A, b, damp=λ, iter_lim=10000)[0]

sparse_kspace = sol.reshape(image_size, image_size)
# sparse_kspace[mask] /= weight_grid[mask]


# Apply a small blur to make the spiral pattern more visible in the visualization
blurred_viz = gaussian_filter(np.abs(sparse_kspace), sigma=1.5)

# Generate coverage mask
coverage_mask = (sparse_kspace != 0).astype(float)

# Reconstruct the image using FFT
reconstructed_image = np.fft.ifft2(np.fft.ifftshift(sparse_kspace))

# de-apod
y, x = np.indices((image_size, image_size))
c = (image_size-1)/2
r = np.sqrt((y-c)**2 + (x-c)**2) / kernel_width_pixels
# KB‐kernel approximation in image domain
apod = np.i0(kernel_beta * np.sqrt(1 - np.minimum(r,1)**2)) / np.i0(kernel_beta)
apod[r>=1] = 0
apod /= apod.max()
# Avoid division by zero
apod += 1e-6

reconstructed_magnitude = np.abs(reconstructed_image) / apod

# Display original image vs reconstruction
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.imshow(image_data, cmap='gray')
plt.title("Original Image")
plt.colorbar()

plt.subplot(1, 2, 2)
plt.imshow(reconstructed_magnitude, cmap='gray')
plt.title("Kernel-based Reconstruction")
plt.colorbar()
plt.tight_layout()
plt.show()

# Calculate k-space coverage
sampled_pixels = np.count_nonzero(sparse_kspace)
total_pixels = image_size * image_size
coverage_percentage = (sampled_pixels / total_pixels) * 100

# Print coverage metric
print(f"K-space Coverage Analysis:")
print(f"  Total k-space pixels: {total_pixels}")
print(f"  Sampled pixels: {sampled_pixels}")
print(f"  Coverage percentage: {coverage_percentage:.2f}%")

# Add coverage visualization to the figure
plt.figure(figsize=(15, 5))
plt.subplot(1, 3, 1)
plt.imshow(np.log(np.abs(kspace) + 1e-10), cmap='gray')
plt.title("Original k-space (Cartesian)")
plt.colorbar()

plt.subplot(1, 3, 2)
plt.imshow(np.log(np.abs(sparse_kspace) + 1e-10), cmap='gray')
plt.title(f"Spiral Sampled k-space\nCoverage: {coverage_percentage:.2f}%")
plt.colorbar()

plt.subplot(1, 3, 3)
plt.imshow(coverage_mask, cmap='binary', vmin=0, vmax=1)
plt.title("K-space Coverage Map")
plt.colorbar(label='Sampled')
plt.tight_layout()
plt.show()

mask = (sparse_kspace != 0)
diff = np.abs(kspace[mask] - sparse_kspace[mask])
print(f"Mean abs error at sampled points: {np.mean(diff):.4e}")
print(f"Max abs error at sampled points: {np.max(diff):.4e}")


# Calculate error metrics
def mse_psnr(ref, cmp):
    mse = np.mean((ref - cmp)**2)
    psnr = 20*np.log10(np.max(ref)/np.sqrt(mse+1e-12))
    return mse, psnr

m1, p1 = mse_psnr(image_data, reconstructed_magnitude)
print(f"No DCF     → MSE={m1:.4e}, PSNR={p1:.2f}dB")

import torch, numpy as np
import matplotlib.pyplot as plt
from torchkbnufft import KbNufftAdjoint, calc_density_compensation_function

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 0. 假设这些变量已在工作区：
#    kx_spiral, ky_spiral, spiral_kspace, image_data, image_size

# 1. 轨迹归一化到 [-π, π] rad/voxel
ktraj_np = np.stack([kx_spiral, ky_spiral])                       # (2, Nk)
ktraj_np = 2*np.pi * ktraj_np / (image_size/2)                    # → rad/voxel
assert np.abs(ktraj_np).max() <= np.pi+1e-6, "ktraj 越界！"
ktraj = torch.tensor(ktraj_np, dtype=torch.float32,
                    device=device).unsqueeze(0)                  # [1,2,Nk]

# 2. 准备 k-space 数据 & DCF
data = torch.from_numpy(spiral_kspace) \
            .unsqueeze(0).unsqueeze(0) \
            .to(device=device, dtype=torch.complex64)           # [1,1,Nk]

dcf  = calc_density_compensation_function(ktraj,
         im_size=(image_size, image_size))                       # [1, Nk] or [1,1,Nk]
if dcf.ndim == 2:                                                # 确保 [1,1,Nk]
    dcf = dcf.unsqueeze(1)
data = data * dcf                                                # 应用 DCF

# 3. adjoint NUFFT + oversampling + 归一化
osf = 2.0
grid_sz = (int(osf*image_size), int(osf*image_size))
nufft_adj = KbNufftAdjoint(im_size=(image_size, image_size),
                           grid_size=grid_sz).to(device)

img_cplx = nufft_adj(data, ktraj) / (image_size * image_size)    # [1,1,Ny,Nx]

# （可选）移除一个全局相位偏移
phi0 = torch.angle(img_cplx[0,0, image_size//2, image_size//2])
img_cplx = img_cplx * torch.exp(-1j * phi0)

recon = img_cplx.abs().cpu().numpy()[0,0]                       # (Ny, Nx)

# 4. 可视化 & 计算指标
plt.figure(figsize=(15,5))
plt.subplot(1,3,1); plt.imshow(image_data, cmap='gray'); plt.title('Original'); plt.colorbar()
plt.subplot(1,3,2); plt.imshow(recon,      cmap='gray'); plt.title('NUFFT recon'); plt.colorbar()
plt.subplot(1,3,3); plt.imshow(np.abs(image_data-recon), cmap='hot')
plt.title('Abs diff'); plt.colorbar()
plt.tight_layout(); plt.show()

mse  = np.mean((image_data-recon)**2)
psnr = 20*np.log10(np.max(image_data)/np.sqrt(mse+1e-12))
print(f"MSE={mse:.4e} | PSNR={psnr:.2f} dB")

def check_trajectory_range(kx_pix, ky_pix, image_size):
    """
    Check the range of trajectory coordinates and print diagnostic information
    """
    # Original pixel coordinates
    kx_min, kx_max = np.min(kx_pix), np.max(kx_pix)
    ky_min, ky_max = np.min(ky_pix), np.max(ky_pix)
    
    # Normalized to [-0.5, 0.5] range expected by BART
    kx_norm = kx_pix / image_size - 0.5
    ky_norm = ky_pix / image_size - 0.5
    kx_norm_min, kx_norm_max = np.min(kx_norm), np.max(kx_norm)
    ky_norm_min, ky_norm_max = np.min(ky_norm), np.max(ky_norm)
    
    print("Trajectory diagnostic information:")
    print(f"Number of points: {len(kx_pix)}")
    print(f"Original kx range: [{kx_min:.2f}, {kx_max:.2f}]")
    print(f"Original ky range: [{ky_min:.2f}, {ky_max:.2f}]")
    print(f"Normalized kx range: [{kx_norm_min:.2f}, {kx_norm_max:.2f}]")
    print(f"Normalized ky range: [{ky_norm_min:.2f}, {ky_norm_max:.2f}]")
    
    # Check if normalized coordinates exceed expected range
    if kx_norm_min < -0.5 or kx_norm_max > 0.5 or ky_norm_min < -0.5 or ky_norm_max > 0.5:
        print("WARNING: Normalized coordinates exceed BART's expected [-0.5, 0.5] range!")
    
    # Check for NaN or inf values
    if np.any(np.isnan(kx_pix)) or np.any(np.isnan(ky_pix)) or np.any(np.isinf(kx_pix)) or np.any(np.isinf(ky_pix)):
        print("WARNING: Trajectory contains NaN or inf values!")
    
    # Check data types
    print(f"Data types - kx_pix: {kx_pix.dtype}, ky_pix: {ky_pix.dtype}")
    
    return {
        "original_range_x": (kx_min, kx_max),
        "original_range_y": (ky_min, ky_max),
        "normalized_range_x": (kx_norm_min, kx_norm_max),
        "normalized_range_y": (ky_norm_min, ky_norm_max),
        "in_range": not (kx_norm_min < -0.5 or kx_norm_max > 0.5 or 
                         ky_norm_min < -0.5 or ky_norm_max > 0.5)
    }

def bart_dcf_calculation_debug(kx_pix, ky_pix, image_size):
    """
    More detailed debugging version for BART DCF calculation
    """
    import numpy as np
    import os
    import subprocess
    import tempfile
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # First, try a simpler approach using BART's own trajectory generation
        try:
            # Generate a test trajectory using BART
            test_traj_path = os.path.join(temp_dir, "test_traj")
            test_cmd = ["bart", "traj", "-x", "32", "-y", "32", test_traj_path]
            print(f"Testing BART traj command: {' '.join(test_cmd)}")
            subprocess.run(test_cmd, check=True)
            
            # Then try weights on this trajectory
            test_dcf_path = os.path.join(temp_dir, "test_dcf")
            test_weights_cmd = [
                "bart", "nufft",
                "-d", f"{image_size}:{image_size}:1",   # ← NEW
                "-w",
                test_traj_path, test_dcf_path
            ]
            print(f"Testing BART weights command: {' '.join(test_weights_cmd)}")
            subprocess.run(test_weights_cmd, check=True)
            
            print("BART test was successful - weights command works with BART-generated trajectory.")
        except Exception as e:
            print(f"BART test failed: {str(e)}")
            print("This suggests issues with the BART installation.")
            return pipe_menon_dcf(kx_pix, ky_pix, kernel_width_pixels, image_size, weight_map, iters=5)
        
        # Now try with our own trajectory
        try:
            # Scale coordinates to [-0.5, 0.5]
            kx_norm = kx_pix / image_size - 0.5
            ky_norm = ky_pix / image_size - 0.5
            
            # BART format: 3 x N array
            num_points = len(kx_norm)
            traj = np.zeros((3, num_points), dtype=np.complex64)
            traj[0, :] = kx_norm
            traj[1, :] = ky_norm
            
            # Save in proper BART format
            traj_path = os.path.join(temp_dir, "traj")
            dcf_path = os.path.join(temp_dir, "dcf")
            
            # Write header
            with open(f"{traj_path}.hdr", "w") as f:
                f.write(f"3 {num_points}")
            
            # Write binary data in Fortran (column-major) order
            # Make sure we convert to complex64 properly
            traj_complex = traj.astype(np.complex64)
            traj_complex.tofile(f"{traj_path}.cfl")
            
            # Print some debug info
            print(f"Trajectory shape: {traj.shape}")
            print(f"First few points: x={traj[0, :5]}, y={traj[1, :5]}")
            
            # Try BART weights command
            cmd = [
                "bart", "nufft",
                "-d", f"{image_size}:{image_size}:1",   # ← NEW
                "-w",
                traj_path, dcf_path
            ]
            print(f"Running BART weights command: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(f"BART command output: {result.stdout}")
            
            # Read the results
            with open(f"{dcf_path}.hdr", "r") as f:
                dims = [int(i) for i in f.read().split()]
                print(f"DCF dimensions: {dims}")
            
            dcf_data = np.fromfile(f"{dcf_path}.cfl", dtype=np.complex64)
            dcf = np.abs(dcf_data)
            
            return dcf
            
        except Exception as e:
            print(f"Error with custom trajectory: {str(e)}")
            print("Falling back to Pipe-Menon method")
            return pipe_menon_dcf(kx_pix, ky_pix, kernel_width_pixels, image_size, weight_map, iters=5)

# Helper functions to read/write CFL files for BART
def write_cfl(name, array):
    """
    Write numpy array to BART cfl format
    """
    h = array.shape
    with open(name + ".hdr", "w") as f:
        f.write(" ".join([str(i) for i in h]))
    with open(name + ".cfl", "wb") as f:
        array.astype(np.complex64).flatten().tofile(f)

def read_cfl(name):
    """
    Read BART cfl format into numpy array
    """
    with open(name + ".hdr", "r") as f:
        h = [int(i) for i in f.read().split()]
    with open(name + ".cfl", "rb") as f:
        a = np.fromfile(f, dtype=np.complex64)
    return a.reshape(h)

def precompute_weight_map(kx_pix, ky_pix, kernel_width_pixels, image_size, kernel_beta):
    weight_map = []
    for xi, yi in zip(kx_pix, ky_pix):
        locals_ = []
        for y in range(max(0, int(yi-kernel_width_pixels)),
                       min(image_size, int(yi+kernel_width_pixels+1))):
            for x in range(max(0, int(xi-kernel_width_pixels)),
                           min(image_size, int(xi+kernel_width_pixels+1))):
                d = np.hypot(x-xi, y-yi) / kernel_width_pixels
                w = kaiser_bessel_kernel(d, width=1.1, beta=kernel_beta)  # ★ 不再归一化
                if w > 0:
                    locals_.append((y, x, w))
        weight_map.append(locals_)
    return weight_map



def pipe_menon_dcf(kx, ky, kernel_width_pixels,
                   image_size, weight_map, iters=5,
                   eps=1e-8):                       # ← 加一个保护常数
    n   = len(kx)
    dcf = np.ones(n, dtype=float)
    for _ in range(iters):
        # 1) Aᴴ(W·s)
        g = grid_spiral_to_cartesian(np.ones(n), weight_map,
                                     image_size, dcf=dcf)

        # 2) A·g
        psf_proj = np.empty(n, float)
        for i, local in enumerate(weight_map):
            val = 0.0 + 0.0j                      # ← 保证复数累加
            for y, x, w in local:
                val += w * g[y, x]
            psf_proj[i] = np.abs(val)             # ← 用模长，纯实数

        # 3) 更新（加 eps 防 0）
        dcf *= (psf_proj + eps) ** (-0.5)
        dcf /= dcf.mean() + eps                  # ← 防止 mean=0
        print(np.min(dcf), np.max(dcf))

    return dcf




def grid_spiral_to_cartesian(spiral_kspace, weight_map, image_size, dcf=None):
            if dcf is None:
                dcf = np.ones_like(spiral_kspace)

            sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
            weight_grid = np.zeros((image_size, image_size), dtype=float)

            for i, local_weights in enumerate(weight_map):
                s_i = spiral_kspace[i] * dcf[i]
                for y, x, weight in local_weights:
                    sparse_kspace[y, x] += weight * s_i
                    weight_grid[y, x] += weight
            mask = weight_grid > 0
            sparse_kspace[mask] /= weight_grid[mask]
            
            return sparse_kspace


def lsqr_deapodized_recon(spiral_kspace, weight_map,
                          image_size, kernel_width, beta,
                          damp=1e-6, max_iter=500):
    """
    LSQR-based gridding + row‐scaling + image‐domain de-apodization.
    """
    # 1. Assemble A·s = b
    n = len(spiral_kspace)
    m = image_size*image_size
    rows, cols, vals = [], [], []
    b = np.zeros(n, dtype=complex)
    W = np.zeros(n, dtype=float)
    for i, local in enumerate(weight_map):
        Wi = sum(w for (_,_,w) in local)
        W[i] = Wi
        b[i] = spiral_kspace[i] * Wi
        for y, x, w in local:
            idx = y*image_size + x
            rows.append(i); cols.append(idx); vals.append(w)
    A = spr.csr_matrix((vals,(rows,cols)), shape=(n, m))

    # 2. Row‐scaling to reduce condition number
    sqrtW = np.sqrt(W + 1e-20)
    b_w = b * sqrtW
    A_w = A.multiply(sqrtW[:,None])

    # 3. LSQR solve with damping (Tikhonov)
    sol = lsqr(A_w, b_w, damp=damp, iter_lim=max_iter)[0]
    sparse_k = sol.reshape(image_size, image_size)

    # 4. Image‐domain reconstruction
    img = np.fft.ifft2(np.fft.ifftshift(sparse_k))

    # 5. De‑apodization (approximate inverse Kaiser–Bessel apod)
    y, x = np.indices((image_size, image_size))
    c = (image_size-1)/2
    r = np.sqrt((y-c)**2 + (x-c)**2) / kernel_width
    # KB‐kernel approximation in image domain
    apod = np.i0(beta * np.sqrt(1 - np.minimum(r,1)**2)) / np.i0(beta)
    apod[r>=1] = 0
    apod /= apod.max()
    # Avoid division by zero
    apod += 1e-6

    # 6. Apply de‑apod and return real image
    return np.abs(img) 

def enhanced_validate_spiral_reconstruction(image_size=90):
    """
    Validate spiral reconstruction using multiple test patterns to 
    thoroughly evaluate accuracy and spatial fidelity
    """
    # 1. Create four synthetic test patterns
    
    # Pattern 1: Simple circle
    circle = np.zeros((image_size, image_size))
    center = image_size // 2
    radius = image_size // 4
    y, x = np.ogrid[:image_size, :image_size]
    circle_mask = (x - center)**2 + (y - center)**2 <= radius**2
    circle[circle_mask] = 1.0
    
    # Pattern 2: Grid of points
    grid = np.zeros((image_size, image_size))
    spacing = image_size // 8
    for i in range(spacing, image_size, spacing):
        for j in range(spacing, image_size, spacing):
            grid[i, j] = 1.0
    
    # Pattern 3: Concentric rings pattern
    rings = np.zeros((image_size, image_size))
    for r in range(radius//4, radius+1, radius//4):
        ring_mask = np.abs((x - center)**2 + (y - center)**2 - r**2) <= (radius//16)**2
        rings[ring_mask] = 1.0
    
    # Pattern 4: Simple text pattern (letter "X")
    text = np.zeros((image_size, image_size))
    thickness = max(1, image_size // 45)
    for i in range(image_size):
        for j in range(image_size):
            # Create diagonals
            if abs(i - j) < thickness or abs(i + j - image_size) < thickness:
                if center - radius < i < center + radius and center - radius < j < center + radius:
                    text[i, j] = 1.0
    
    # 2. Test all patterns
    patterns = [circle, grid, rings, text]
    pattern_names = ["Circle", "Point Grid", "Concentric Rings", "Letter X"]
    
    results = []
    
    for pattern, name in zip(patterns, pattern_names):
        # Convert to k-space (Cartesian)
        kspace = np.fft.fftshift(np.fft.fft2(pattern))
        
        # Sample along spiral trajectory
        spiral_kspace = np.zeros(len(kx_pixel_float), dtype=complex)
        for i in range(len(kx_pixel_float)):
            # Use kernel-based interpolation for each spiral point
            value = 0.0
            total_weight = 0.0
            x_center = kx_pixel_float[i]
            y_center = ky_pixel_float[i]
            
            # Use the same kernel width and function as in main code
            for y in range(max(0, int(y_center - kernel_width_pixels)), 
                          min(image_size, int(y_center + kernel_width_pixels + 1))):
                for x in range(max(0, int(x_center - kernel_width_pixels)), 
                              min(image_size, int(x_center + kernel_width_pixels + 1))):
                    distance = np.sqrt((x - x_center)**2 + (y - y_center)**2)
                    weight = kaiser_bessel_kernel(distance / kernel_width_pixels, width=1.1, beta=kernel_beta)
                    if weight > 0:
                        value += weight * kspace[y, x]
                        total_weight += weight
            
            if total_weight > 0:
                spiral_kspace[i] = value
        

        weight_map = precompute_weight_map(kx_pixel_float,
                                   ky_pixel_float,
                                   kernel_width_pixels,
                                   image_size,kernel_beta)

        dcf = pipe_menon_dcf(kx_pixel_float, ky_pixel_float,      # ← 改这两个
                     kernel_width_pixels, image_size,
                     weight_map, iters=5)
        
        dcf = bart_dcf_calculation_debug(kx_pixel_float, ky_pixel_float, image_size)
        
        # Grid back to Cartesian
        recon_kspace = grid_spiral_to_cartesian(spiral_kspace, weight_map, image_size, dcf=dcf)
        
        # Reconstruct image
        recon_image = np.abs(np.fft.ifft2(np.fft.ifftshift(recon_kspace)))

        # recon_image = lsqr_deapodized_recon(spiral_kspace, weight_map,
        #                 image_size, kernel_width_pixels, kernel_beta,
        #                 damp=1e-6, max_iter=10000
        #             )
        
        # Calculate error metrics
        mse = np.mean((pattern - recon_image)**2)
        psnr = 20 * np.log10(np.max(pattern) / np.sqrt(mse + 1e-12))
        
        # Store results
        results.append({
            'name': name,
            'original': pattern,
            'reconstructed': recon_image,
            'mse': mse,
            'psnr': psnr
        })
        
        # Display results
        plt.figure(figsize=(15, 5))
        plt.subplot(1, 3, 1)
        plt.imshow(pattern, cmap='gray')
        plt.title(f"Original {name}")
        plt.axis('off')
        
        plt.subplot(1, 3, 2)
        plt.imshow(recon_image, cmap='gray')
        plt.title(f"Reconstructed {name}")
        plt.axis('off')
        
        plt.subplot(1, 3, 3)
        plt.imshow(np.abs(pattern - recon_image), cmap='hot', vmin=0, vmax=0.2)
        plt.title(f"Error (MSE={mse:.4f}, PSNR={psnr:.2f}dB)")
        plt.colorbar()
        plt.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    # 3. Summary of results
    print("\nReconstruction Quality Summary:")
    print("="*50)
    print(f"{'Pattern':<20} {'MSE':<10} {'PSNR (dB)':<10}")
    print("-"*50)
    for result in results:
        print(f"{result['name']:<20} {result['mse']:.4f}     {result['psnr']:.2f}")
    print("="*50)
    
    return results

# Run the enhanced validation
results = enhanced_validate_spiral_reconstruction(image_size=90)

# def gridding_reconstruction(spiral_kspace, kx_spiral, ky_spiral, image_size, dcf=None, kernel_width=3.0):
#     """
#     Reconstruct image from non-Cartesian spiral k-space data using gridding
    
#     Parameters:
#     -----------
#     spiral_kspace : ndarray
#         Complex k-space data samples along the spiral trajectory
#     kx_spiral, ky_spiral : ndarray
#         k-space coordinates of the spiral trajectory (in pixel units)
#     image_size : int
#         Size of the reconstructed image (assumed square)
#     dcf : ndarray or None
#         Density compensation factors (calculated if None)
#     kernel_width : float
#         Width of the gridding kernel
        
#     Returns:
#     --------
#     reconstructed_image : ndarray
#         The reconstructed image
#     """
#     import numpy as np
#     from scipy.interpolate import Rbf
#     from scipy.ndimage import gaussian_filter
    
#     # --- STEP 1: Density Compensation ---
#     if dcf is None:
#         # Calculate radial distance from center
#         kx_rel = kx_spiral - image_size/2
#         ky_rel = ky_spiral - image_size/2
#         r = np.sqrt(kx_rel**2 + ky_rel**2)
#         max_r = np.max(r)
        
#         # Calculate density compensation factors (DCF)
#         # For spiral: DCF ~ r (radial distance)
#         dcf = np.sqrt(r / max_r + 0.1)  # Add small offset to avoid zero at center
    
#     # Apply density compensation
#     compensated_kspace = spiral_kspace * dcf
    
#     # --- STEP 2 & 3: Convolution with Kernel and Gridding ---
#     # Create Cartesian grid
#     kx_cart, ky_cart = np.meshgrid(np.arange(image_size), np.arange(image_size))
#     kx_cart_flat = kx_cart.flatten()
#     ky_cart_flat = ky_cart.flatten()
    
#     # Create sparse k-space representation for visualization
#     sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    
#     # Round coordinates to nearest integer for initial gridding
#     kx_int = np.clip(np.round(kx_spiral).astype(int), 0, image_size-1)
#     ky_int = np.clip(np.round(ky_spiral).astype(int), 0, image_size-1)
    
#     # Apply kernel to neighborhood of each sample point
#     for i in range(len(kx_spiral)):
#         x, y = kx_int[i], ky_int[i]
#         sparse_kspace[y, x] = compensated_kspace[i]
    
#     # Apply gridding kernel via convolution (Gaussian approximation of Kaiser-Bessel)
#     kernel_sigma = kernel_width / 2.5  # Relationship between width and sigma
#     gridded_kspace_real = gaussian_filter(sparse_kspace.real, sigma=kernel_sigma)
#     gridded_kspace_imag = gaussian_filter(sparse_kspace.imag, sigma=kernel_sigma)
#     gridded_kspace = gridded_kspace_real + 1j * gridded_kspace_imag
    
#     # --- STEP 4: FFT Reconstruction ---
#     reconstructed_image = np.fft.ifft2(np.fft.ifftshift(gridded_kspace))
    
#     # --- STEP 5: De-apodization ---
#     # Generate de-apodization function (inverse of gridding kernel in image domain)
#     x = np.arange(-image_size//2, image_size//2)
#     y = np.arange(-image_size//2, image_size//2)
#     X, Y = np.meshgrid(x, y)
#     R = np.sqrt(X**2 + Y**2)
    
#     # Gaussian kernel in image domain
#     deapod = np.exp((R * np.pi * kernel_sigma / image_size)**2 / 2)
    
#     # Apply de-apodization
#     final_image = reconstructed_image * deapod
    
#     return final_image


# # Get the reconstructed image
# reconstructed_image = gridding_reconstruction(
#     spiral_kspace= k,  # Your spiral k-space data 
#     kx_spiral=kx,           # x-coordinates of spiral trajectory
#     ky_spiral=ky,           # y-coordinates of spiral trajectory
#     image_size=image_size,        # Output image dimensions
#     dcf=density_comp              # Your existing density compensation
# )

# # Display the result
# plt.figure(figsize=(12, 5))
# plt.subplot(1, 2, 1)
# plt.imshow(np.abs(reconstructed_image), cmap='gray')
# plt.title('Gridding Reconstruction')
# plt.colorbar()

# plt.subplot(1, 2, 2)
# plt.imshow(image_data, cmap='gray')
# plt.title('Reference Image')
# plt.colorbar()

# plt.tight_layout()
# plt.show()

# import numpy as np
# import matplotlib.pyplot as plt
# from scipy.ndimage import gaussian_filter

# # Convert the PyPulseq k-space trajectory to match our image dimensions
# # Get input T2*+ GRE image and convert to k-space
# image_data = gre_t2star_image
# image_size = image_data.shape[0]  # Should be 150
# print(f"Input image shape: {image_data.shape}")


# # Create a sampling mask from the spiral trajectory
# sampling_mask = np.zeros((image_size, image_size), dtype=bool)
# for i in range(len(kx_pixel)):
#     x, y = kx_pixel[i], ky_pixel[i]
#     sampling_mask[y, x] = True

# # Count coverage
# coverage = np.sum(sampling_mask) / (image_size * image_size) * 100
# print(f"k-space coverage: {coverage:.2f}% of pixels")

# # Calculate density compensation function (DCF) - radial distance based
# r_norm = np.sqrt((kx/k_max)**2 + (ky/k_max)**2)  # Normalized radius [0,1]
# dcf = np.sqrt(r_norm + 0.1)  # Simple DCF with small offset to avoid zero at center

# # ===== Generate synthetic coil sensitivity maps =====
# def generate_coil_sensitivity_maps(image_size, n_coils=8, coil_radius=1.2):
#     """Generate synthetic coil sensitivity maps with rectangular FOV"""
#     maps = np.zeros((n_coils, image_size, image_size), dtype=complex)
    
#     # Create a grid of coordinates
#     x = np.linspace(-1, 1, image_size)
#     y = np.linspace(-1, 1, image_size)
#     X, Y = np.meshgrid(x, y)
    
#     # Define coil positions around a rectangular FOV (match image dimensions)
#     # Use an elliptical layout to better match rectangular FOV
#     aspect_ratio = 1.0  # Adjust if your FOV isn't square
#     theta = np.linspace(0, 2*np.pi, n_coils, endpoint=False)
#     coil_x = coil_radius * np.cos(theta)
#     coil_y = coil_radius * aspect_ratio * np.sin(theta)
    
#     # Generate sensitivity profile for each coil
#     for i in range(n_coils):
#         # Distance from each pixel to the coil
#         distance = np.sqrt((X - coil_x[i])**2 + (Y - coil_y[i])**2)
        
#         # Sensitivity falls off with distance (Gaussian model)
#         sensitivity = np.exp(-distance**2 / (2 * 0.7**2))
        
#         # Add some phase variation
#         phase = np.angle(X + 1j*Y) * 0.3 - theta[i]
#         complex_sensitivity = sensitivity * np.exp(1j * phase)
        
#         # Apply a rectangular body mask to match FOV shape
#         body_mask = (np.abs(X) <= 0.9) & (np.abs(Y) <= 0.9)
#         maps[i, body_mask] = complex_sensitivity[body_mask]
        
#         # Smooth the sensitivity map
#         maps[i] = gaussian_filter(maps[i].real, sigma=0.8) + 1j * gaussian_filter(maps[i].imag, sigma=0.8)
    
#     # Normalize the combined sensitivity maps
#     # This ensures consistent brightness across different coil counts
#     sum_of_squares = np.sqrt(np.sum(np.abs(maps)**2, axis=0))
#     valid_mask = sum_of_squares > 0
    
#     for i in range(n_coils):
#         maps[i, valid_mask] = maps[i, valid_mask] / sum_of_squares[valid_mask]
    
#     return maps

# # Generate coil sensitivity maps for different numbers of coils
# coil_configs = [1, 8, 32]
# all_sensitivity_maps = {}
# for n_coils in coil_configs:
#     all_sensitivity_maps[n_coils] = generate_coil_sensitivity_maps(image_size, n_coils)
#     print(f"Generated {n_coils}-channel coil sensitivity maps")

# # Visualize the coil sensitivity maps for the 8-coil configuration
# plt.figure(figsize=(15, 8))
# n_coils_to_show = 8
# sensitivity_maps = all_sensitivity_maps[n_coils_to_show]
# for i in range(n_coils_to_show):
#     plt.subplot(2, 4, i+1)
#     plt.imshow(np.abs(sensitivity_maps[i]), cmap='viridis')
#     plt.title(f"Coil {i+1} Sensitivity")
#     plt.axis('off')
# plt.tight_layout()
# plt.show()

# # ===== Generate undersampled multi-coil k-space data =====
# def generate_multicoil_kspace(image, sensitivity_maps, sampling_mask=None):
#     """Generate multi-coil k-space data with optional undersampling"""
#     n_coils = sensitivity_maps.shape[0]
#     size_x, size_y = image.shape
    
#     # Apply coil sensitivity maps to generate individual coil images
#     coil_images = np.zeros((n_coils, size_x, size_y), dtype=complex)
#     for i in range(n_coils):
#         coil_images[i] = image * sensitivity_maps[i]
    
#     # Transform to k-space
#     noise_level = 0.01  # Adjust based on desired SNR
#     coil_kspace = np.zeros_like(coil_images)
#     for i in range(n_coils):
#         coil_kspace[i] = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(coil_images[i])))
#         coil_kspace[i] += noise_level * (np.random.normal(0, 1, coil_kspace[i].shape) + 
#                                     1j * np.random.normal(0, 1, coil_kspace[i].shape))
    
#     # Apply sampling mask if provided
#     if sampling_mask is not None:
#         for i in range(n_coils):
#             coil_kspace[i] *= sampling_mask
    
#     return coil_kspace, coil_images

# # ===== Implement SENSE reconstruction =====
# def sense_reconstruction(undersampled_kspace, sensitivity_maps, sampling_mask, dcf=None, lambda_reg=1e-3, max_iter=30):
#     """
#     SENSE reconstruction using iterative conjugate gradient method
    
#     Parameters:
#     -----------
#     undersampled_kspace : ndarray [n_coils, nx, ny]
#         Undersampled k-space data from each coil
#     sensitivity_maps : ndarray [n_coils, nx, ny]
#         Coil sensitivity maps
#     sampling_mask : ndarray [nx, ny]
#         Binary mask indicating sampled k-space points
#     dcf : ndarray [nx, ny] or None
#         Density compensation function
#     lambda_reg : float
#         Regularization parameter for stabilizing the reconstruction
#     max_iter : int
#         Maximum number of iterations for conjugate gradient
        
#     Returns:
#     --------
#     recon_image : ndarray [nx, ny]
#         Reconstructed image
#     """
#     n_coils, nx, ny = undersampled_kspace.shape
    
#     # Scale regularization with channel count
#     lambda_reg = lambda_reg * (n_coils / 8)  
    
#     # Apply density compensation if provided
#     if dcf is not None:
#         for c in range(n_coils):
#             undersampled_kspace[c] *= sampling_mask * dcf
    
#     # Gridding - apply Gaussian smoothing to k-space
#     gridded_kspace = np.zeros_like(undersampled_kspace)
#     for c in range(n_coils):
#         gridded_kspace[c] = gaussian_filter(undersampled_kspace[c].real, sigma=0.5) + \
#                            1j * gaussian_filter(undersampled_kspace[c].imag, sigma=0.5)
    
#     # Transform to image domain
#     coil_images = np.zeros_like(gridded_kspace)
#     for c in range(n_coils):
#         coil_images[c] = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(gridded_kspace[c])))
    
#     # Initialize result with sum-of-squares as initial guess
#     recon_image = np.sum(coil_images * np.conj(sensitivity_maps), axis=0)
#     sensitivity_sum = np.sum(np.abs(sensitivity_maps)**2, axis=0)
#     sensitivity_sum[sensitivity_sum < 1e-10] = 1e-10  # Avoid division by zero
#     recon_image /= sensitivity_sum
    
#     # Reshape for easier matrix operations
#     m = coil_images.reshape(n_coils, nx*ny)
#     S = sensitivity_maps.reshape(n_coils, nx*ny)
#     x = recon_image.flatten()
    
#     # Define forward operation: S*x
#     def forward_op(x_input):
#         return np.sum(S * x_input.reshape(1, nx*ny), axis=1)
    
#     # Define adjoint operation: S^H*y
#     def adjoint_op(y_input):
#         # Handle different input shapes correctly
#         if y_input.size == n_coils * nx * ny:
#             # If input is flattened entire array
#             y_reshaped = y_input.reshape(n_coils, nx*ny)
#             return np.sum(np.conj(S) * y_reshaped, axis=0)
#         else:
#             # If input is just the coil dimension
#             return np.sum(np.conj(S) * y_input.reshape(n_coils, 1), axis=0)
    
#     # Define normal operation: (S^H*S + λI)*x
#     def normal_op(x_input):
#         return adjoint_op(forward_op(x_input)) + lambda_reg * x_input
    
#     # Conjugate Gradient algorithm
#     # 2. Ensure proper normalization in the conjugate gradient steps
#     adjoint_m = np.zeros(nx*ny, dtype=complex)
#     for c in range(n_coils):
#         adjoint_m += np.conj(S[c]) * m[c]
    
#     # 3. Apply stronger initial normalization
#     x = adjoint_m / (np.sum(np.abs(S)**2, axis=0) + lambda_reg)
    
#     # 4. Implement a more stable CG loop with convergence checks
#     r = adjoint_m - normal_op(x)
#     p = r.copy()
#     rsold = np.vdot(r, r)
    
#     # Add early stopping if residual becomes unstable
#     for i in range(max_iter):
#         Ap = normal_op(p)
#         alpha = rsold / (np.vdot(p, Ap) + 1e-15)  # Avoid division by zero
        
#         # Check for unstable values
#         if np.isnan(alpha) or np.isinf(alpha) or np.abs(alpha) > 1e6:
#             print(f"Warning: CG diverging at iteration {i}, stopping early")
#             break
            
#         x = x + alpha * p
#         r = r - alpha * Ap
#         rsnew = np.vdot(r, r)
        
#         # 5. Check for convergence or divergence
#         if np.sqrt(rsnew) < 1e-6 or rsnew > rsold*10:
#             break
            
#         p = r + (rsnew / rsold) * p
#         rsold = rsnew
    
#     # 6. Apply final robust normalization
#     recon_image = x.reshape(nx, ny)
    
#     # 7. Scale output to match reference image intensity
#     scale_factor = np.mean(np.abs(image_data)) / np.mean(np.abs(recon_image))
#     recon_image *= scale_factor
    
#     return recon_image

# # ===== Generate a multi-coil DCF for spiral =====
# def apply_multicoil_dcf_to_kspace(kspace, kx_pixel_int, ky_pixel_int, dcf):
#     """Apply density compensation to each point in the spiral trajectory"""
#     n_coils, nx, ny = kspace.shape
#     kspace_with_dcf = kspace.copy()
    
#     for c in range(n_coils):
#         for i in range(len(kx_pixel_int)):
#             x, y = kx_pixel_int[i], ky_pixel_int[i]
#             kspace_with_dcf[c, y, x] = kspace[c, y, x] * dcf[i]
            
#     return kspace_with_dcf

# # Create the DCF array for the k-space sampling pattern
# dcf_map = np.zeros((image_size, image_size))
# for i in range(len(kx_pixel)):
#     x, y = kx_pixel[i], ky_pixel[i]
#     dcf_map[y, x] = dcf[i]

# # Compare reconstructions with different numbers of coils
# results = {}
# for n_coils in coil_configs:
#     sensitivity_maps = all_sensitivity_maps[n_coils]
    
#     # Generate multi-coil k-space data
#     multicoil_kspace, multicoil_images = generate_multicoil_kspace(image_data, sensitivity_maps)
    
#     # Apply spiral sampling mask - keep only the points on the trajectory
#     sparse_multicoil_kspace = np.zeros_like(multicoil_kspace)
#     for c in range(n_coils):
#         for i in range(len(kx_pixel)):
#             x, y = kx_pixel[i], ky_pixel[i]
#             sparse_multicoil_kspace[c, y, x] = multicoil_kspace[c, y, x]
    
#     # Apply density compensation to sparse k-space
#     sparse_multicoil_kspace = apply_multicoil_dcf_to_kspace(
#         sparse_multicoil_kspace, kx_pixel, ky_pixel, dcf
#     )
    
#     # Perform SENSE reconstruction
#     reconstructed_sense = sense_reconstruction(
#         sparse_multicoil_kspace, sensitivity_maps, sampling_mask, None, lambda_reg=1e-3
#     )
    
#     # Simple sum-of-squares reconstruction for comparison
#     gridded_kspace = np.zeros_like(sparse_multicoil_kspace)
#     for c in range(n_coils):
#         gridded_kspace[c] = gaussian_filter(sparse_multicoil_kspace[c].real, sigma=0.5) + \
#                            1j * gaussian_filter(sparse_multicoil_kspace[c].imag, sigma=0.5)
    
#     sos_images = np.zeros_like(sparse_multicoil_kspace, dtype=complex)
#     for c in range(n_coils):
#         sos_images[c] = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(gridded_kspace[c])))
    
#     reconstructed_sos = np.sqrt(np.sum(np.abs(sos_images)**2, axis=0)) / np.sqrt(n_coils)
    
#     # Calculate error metrics
#     mse_sense = np.mean(np.abs(image_data - np.abs(reconstructed_sense))**2)
#     psnr_sense = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sense))
    
#     mse_sos = np.mean(np.abs(image_data - reconstructed_sos)**2)
#     psnr_sos = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sos))
    
#     results[n_coils] = {
#         'sense': np.abs(reconstructed_sense),
#         'sos': reconstructed_sos,
#         'mse_sense': mse_sense,
#         'psnr_sense': psnr_sense,
#         'mse_sos': mse_sos,
#         'psnr_sos': psnr_sos
#     }
    
#     print(f"{n_coils}-channel reconstruction:")
#     print(f"  SENSE: MSE={mse_sense:.5f}, PSNR={psnr_sense:.2f}dB")
#     print(f"  SoS:   MSE={mse_sos:.5f}, PSNR={psnr_sos:.2f}dB")

# # Visualize the reconstruction results
# # Create a figure with rows for different coil counts and columns for different methods
# plt.figure(figsize=(15, 12))

# # Original image
# plt.subplot(len(coil_configs)+1, 3, 1)
# plt.imshow(image_data, cmap='gray')
# plt.title("Original Image")
# plt.axis('off')

# plt.subplot(len(coil_configs)+1, 3, 2)
# plt.imshow(np.log(np.abs(kspace) + 1), cmap='viridis')
# plt.title("Full k-space")
# plt.axis('off')

# plt.subplot(len(coil_configs)+1, 3, 3)
# plt.plot(kx_pixel - image_size/2, ky_pixel - image_size/2, 'b.', markersize=1)
# plt.title(f"Spiral Trajectory\n({coverage:.1f}% coverage)")
# plt.axis('equal')
# plt.grid(True)

# # Show reconstructions for different numbers of coils
# for i, n_coils in enumerate(coil_configs):
#     row = i + 1
    
#     # SENSE reconstruction
#     plt.subplot(len(coil_configs)+1, 3, row*3 + 1)
#     plt.imshow(results[n_coils]['sense'], cmap='gray')
#     plt.title(f"{n_coils}-ch SENSE\nPSNR={results[n_coils]['psnr_sense']:.1f}dB")
#     plt.axis('off')
    
#     # Sum-of-squares reconstruction
#     plt.subplot(len(coil_configs)+1, 3, row*3 + 2)
#     plt.imshow(results[n_coils]['sos'], cmap='gray')
#     plt.title(f"{n_coils}-ch SoS\nPSNR={results[n_coils]['psnr_sos']:.1f}dB")
#     plt.axis('off')
    
#     # Error map
#     plt.subplot(len(coil_configs)+1, 3, row*3 + 3)
#     plt.imshow(np.abs(image_data - results[n_coils]['sense']), cmap='hot', vmin=0, vmax=0.2)
#     plt.title(f"SENSE Error\nMSE={results[n_coils]['mse_sense']:.5f}")
#     plt.axis('off')

# plt.tight_layout()
# plt.show()

# # Plot PSNR vs number of coils
# plt.figure(figsize=(10, 5))
# plt.plot(coil_configs, [results[n]['psnr_sense'] for n in coil_configs], 'bo-', linewidth=2, label='SENSE')
# plt.plot(coil_configs, [results[n]['psnr_sos'] for n in coil_configs], 'ro-', linewidth=2, label='Sum-of-squares')
# plt.xlabel('Number of Coils')
# plt.ylabel('PSNR (dB)')
# plt.title('Reconstruction Quality vs. Number of Coils')
# plt.grid(True)
# plt.legend()
# plt.tight_layout()
# plt.show()

# # Zoomed cardiac region comparison (using the best reconstruction)
# # Find cardiac center
# cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
# for label in [1, 2, 5, 6]:  # Cardiac structures
#     cardiac_mask |= (data['Labels'][:,:,z_slice] == label)

# if np.any(cardiac_mask):
#     y_indices, x_indices = np.where(cardiac_mask)
#     zoom_y, zoom_x = int(np.mean(y_indices)), int(np.mean(x_indices))
# else:
#     zoom_y, zoom_x = image_size // 2, image_size // 2

# # Define zoom parameters
# zoom_size = 40
# zoom_y_min = max(0, zoom_y-zoom_size)
# zoom_y_max = min(image_size, zoom_y+zoom_size)
# zoom_x_min = max(0, zoom_x-zoom_size)
# zoom_x_max = min(image_size, zoom_x+zoom_size)

# # Compare zoomed regions with best reconstruction (32-channel)
# plt.figure(figsize=(15, 5))

# # Original zoomed
# plt.subplot(1, 4, 1)
# plt.imshow(image_data[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
# plt.title("Original (Zoomed)")
# plt.axis('off')

# # 32-ch SENSE zoomed
# plt.subplot(1, 4, 2)
# plt.imshow(results[1]['sense'][zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
# plt.title("32-ch SENSE (Zoomed)")
# plt.axis('off')

# # 32-ch SoS zoomed
# plt.subplot(1, 4, 3)
# plt.imshow(results[1]['sos'][zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
# plt.title("32-ch SoS (Zoomed)")
# plt.axis('off')

# # Error map zoomed
# plt.subplot(1, 4, 4)
# plt.imshow(np.abs(image_data - results[32]['sense'])[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], 
#           cmap='hot', vmin=0, vmax=0.2)
# plt.title("Error (Zoomed)")
# plt.colorbar()
# plt.axis('off')

# plt.tight_layout()
# plt.show()

# # Print summary table
# print("\nMulti-Coil Spiral Reconstruction Summary:")
# print(f"{'Coils':<6} {'Sampling':<10} {'SENSE PSNR':<15} {'SoS PSNR':<15} {'SENSE/SoS Gain':<15}")
# print("-" * 65)

# for n_coils in coil_configs:
#     psnr_gain = results[n_coils]['psnr_sense'] - results[n_coils]['psnr_sos']
#     print(f"{n_coils:<6} {coverage:.1f}%{'':<6} {results[n_coils]['psnr_sense']:.2f} dB{'':<5} "
#           f"{results[n_coils]['psnr_sos']:.2f} dB{'':<5} {psnr_gain:.2f} dB")

import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter
import math

# Parameters for simulation
image_size = reconstructed_image.shape[0]
# fov = 18  # Use fov_xy from previous cell (150e-3)

readout_duration = time[-1] # second


# Use sampling times from the previous cell
# Instead of calculating new ones, use the time vector t from the spiral trajectory generation
t = np.linspace(0, readout_duration, num=len(kx_spiral))
sampling_times = t  # This should be the time vector from the spiral creation cell

# Rest of the code remains the same...
# readout_duration = t[-1]  # Use the actual maximum time from the previous cell
print(f"Using readout duration: {readout_duration*1000:.2f} ms")

# Function to simulate T2* decay effects during spiral acquisition
def simulate_spiral_t2star_decay(kspace_original, ky_pixel, kx_pixel, orig_image, t2star_map, b0_map, sampling_times,weight_map):
    """
    Simulate T2* decay and B0 effects in spiral MRI acquisition
    
    Parameters:
    -----------
    kspace_original: Complex array of spiral k-space data
    t2star_map: T2* values in milliseconds
    b0_map: B0 field inhomogeneity map in Hz
    sampling_times: Acquisition time for each k-space point
    
    Returns:
    --------
    kspace_with_decay: Complex array with T2* and B0 effects
    """
    image_size = 90
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    weight_grid = np.zeros((image_size, image_size), dtype=float)
    for i, local_weights in enumerate(weight_map):
        for y, x, weight in local_weights:
            sparse_kspace[y, x] += weight * kspace_original[i]
            weight_grid[y, x] += weight

    mask = weight_grid > 0
    sparse_kspace[mask] /= weight_grid[mask]

    # Create sparse Cartesian k-space
    # sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    # sparse_kspace[ky_pixel, kx_pixel] = kspace_original

    # kspace_original = np.fft.fftshift(np.fft.fft2(orig_image))
    
    print(f'size of kspace original: {kspace_original.shape}')
    
    # Apply minimal smoothing for better visual quality
    real_part = gaussian_filter(sparse_kspace.real, sigma=0.3)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=0.3)
    smoothed_kspace = real_part + 1j * imag_part
    
    # Convert to image domain
    orig_image = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(smoothed_kspace)))
    orig_image = np.fft.ifft2(np.fft.ifftshift(smoothed_kspace))
    
    # Convert T2* map from ms to seconds
    t2star_sec = t2star_map / 1000.0
    
    # Avoid too small T2* values
    min_t2s = 5.0 / 1000.0  # 5 ms minimum
    t2star_sec = np.maximum(t2star_sec, min_t2s)
    
    # Initialize output
    kspace_with_decay = np.zeros_like(kspace_original, dtype=complex)
    
    # Use time binning for efficient processing
    time_bin = 0.001  # 1ms bins
    time_bins = np.arange(0, np.max(sampling_times) + time_bin, time_bin)

    print(f'kx_pixel size: {len(kx_pixel)}')
    print(f'ky_pixel size: {len(ky_pixel)}')
    # print(f'size of kspace original')
    
    # Process each time bin
    for i, t in enumerate(time_bins[:-1]):
        # Find points in this time bin
        time_indices = np.where((sampling_times >= t) & (sampling_times < time_bins[i+1]))[0]
        
        if len(time_indices) == 0:
            continue
        
        # Calculate T2* decay at this time
        t_mid = (t + time_bins[i+1]) / 2  # Use middle of time bin
        decay = np.exp(-t_mid / t2star_sec)
        
        # Calculate phase from B0 field
        b0_phase = np.exp(-1j * 2 * np.pi * b0_map * t_mid)
        
        # Apply both effects to the original image
        decayed_image = orig_image * decay * b0_phase
        
        # Convert back to k-space
        # decayed_kspace = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(decayed_image)))
        decayed_kspace = np.fft.fftshift(np.fft.fft2(decayed_image))
        
        # # Sample at spiral trajectory points for this time bin
        # for idx in time_indices:
        #     x_idx = kx_pixel[idx]
        #     y_idx = ky_pixel[idx]
        #     kspace_with_decay[idx] = decayed_kspace[y_idx, x_idx]

        for idx in time_indices:
            # 用 weight_map[idx] 对 decayed_kspace 做加权采样
            value = 0.0
            total_weight = 0.0
            for y, x, weight in weight_map[idx]:
                value += weight * decayed_kspace[y, x]
                total_weight += weight
            if total_weight > 0:
                kspace_with_decay[idx] = value / total_weight
            else:
                kspace_with_decay[idx] = 0.0
    
    return kspace_with_decay, orig_image

# Function for pointwise simulation without time binning(time consumping)
def simulate_spiral_t2star_decay_pointwise(kspace_original, t2star_map, b0_map, sampling_times):
    """
    逐点模拟T2*衰减和B0效应，不使用时间分箱
    
    Parameters:
    -----------
    kspace_original: 原始螺旋k空间数据
    t2star_map: T2*值（毫秒）
    b0_map: B0场不均匀性（Hz）
    sampling_times: 每个k空间点的采样时间
    
    Returns:
    --------
    kspace_with_decay: 包含T2*和B0效应的k空间数据
    """
    # 创建稀疏笛卡尔k空间
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    sparse_kspace[ky_pixel, kx_pixel] = kspace_original
    
    # 应用平滑以改善视觉质量
    real_part = gaussian_filter(sparse_kspace.real, sigma=0.5)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=0.5)
    smoothed_kspace = real_part + 1j * imag_part
    
    # 转换到图像空间
    orig_image = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(smoothed_kspace)))
    
    # 转换T2*值从毫秒到秒
    t2star_sec = np.maximum(t2star_map / 1000.0, 5.0/1000.0)  # 设置5ms最小值
    
    # 初始化输出
    kspace_with_decay = np.zeros_like(kspace_original, dtype=complex)
    
    # 逐点处理
    for idx in range(len(sampling_times)):
        # 获取该点的采样时间
        t = sampling_times[idx]
        
        # 计算该时间点的T2*衰减
        decay = np.exp(-t / t2star_sec)
        
        # 计算B0引起的相位
        b0_phase = np.exp(-1j * 2 * np.pi * b0_map * t)
        
        # 将两种效应应用于原始图像
        decayed_image = orig_image * decay * b0_phase
        
        # 转回k空间
        decayed_kspace = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(decayed_image)))
        
        # 提取该螺旋轨迹点对应的k空间值
        x_idx = kx_pixel[idx]
        y_idx = ky_pixel[idx]
        kspace_with_decay[idx] = decayed_kspace[y_idx, x_idx]
        
        # 可选的进度报告
        if idx % 5000 == 0:
            print(f"处理螺旋轨迹点: {idx}/{len(sampling_times)}")
    
    return kspace_with_decay, orig_image


r_max = image_size/2



kx_pixel = np.clip(((kx_spiral / k_max) * (image_size/2) + image_size/2).astype(int), 0, image_size-1)
ky_pixel = np.clip(((ky_spiral / k_max) * (image_size/2) + image_size/2).astype(int), 0, image_size-1)

def grid_spiral_to_cartesian(spiral_kspace, weight_map, image_size):
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    weight_grid = np.zeros((image_size, image_size), dtype=float)
    for i, local_weights in enumerate(weight_map):
        for y, x, weight in local_weights:
            sparse_kspace[y, x] += weight * spiral_kspace[i]
            weight_grid[y, x] += weight
    mask = weight_grid > 0
    sparse_kspace[mask] /= weight_grid[mask]
    return sparse_kspace



# Apply gridding reconstruction (Gaussian smoothing for demonstration)
def apply_gridding(sparse_kspace, weight_grid=None, sigma=0.3):
    real_part = gaussian_filter(sparse_kspace.real, sigma=sigma)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=sigma)
    smoothed_kspace = real_part + 1j * imag_part
    # 如果有权重，归一化
    # if weight_grid is not None:
    #     mask = weight_grid > 0
    #     smoothed_kspace[mask] /= weight_grid[mask]

    recon = np.fft.ifft2(np.fft.ifftshift(smoothed_kspace))
    return np.abs(recon)


# Simulate four versions for comparison
print("Simulating T2* and B0 effects...")
# 1. Without decay (original)
# 2. With T2* decay only (no B0)
# 3. With both T2* and B0 effects
# 4. With T2* and shimmed B0 effects
print('b0_map size:', b0_map.shape)
print('shimmed_b0_map size:', shimb0_map.shape)
spiral_raw_kspace = spiral_kspace.copy()

# Original (no decay)
spiral_kspace_original = spiral_raw_kspace.copy()

# T2* decay only
spiral_kspace_t2star, orig_image = simulate_spiral_t2star_decay(
    spiral_raw_kspace, ky_pixel, kx_pixel, gre_t2star_image, t2star_plus_map, np.zeros_like(b0_map), sampling_times, weight_map
)

# T2* and original B0 effects
spiral_kspace_t2star_b0, _ = simulate_spiral_t2star_decay(
    spiral_raw_kspace, ky_pixel, kx_pixel, gre_t2star_image, t2star_plus_map, b0_map, sampling_times, weight_map
)

# T2* and shimmed B0 effects (new)
spiral_kspace_t2star_shimb0, _ = simulate_spiral_t2star_decay(
    spiral_raw_kspace, ky_pixel, kx_pixel, gre_t2star_image, t2star_plus_map, shimb0_map, sampling_times, weight_map
)

# Grid all versions to Cartesian coordinates
sparse_kspace_original = grid_spiral_to_cartesian(spiral_kspace_original, weight_map, image_size)
sparse_kspace_t2star = grid_spiral_to_cartesian(spiral_kspace_t2star, weight_map, image_size)
sparse_kspace_t2star_b0 = grid_spiral_to_cartesian(spiral_kspace_t2star_b0, weight_map, image_size)
sparse_kspace_t2star_shimb0 = grid_spiral_to_cartesian(spiral_kspace_t2star_shimb0, weight_map, image_size)

# Reconstruct images
recon_original = apply_gridding(sparse_kspace_original)
recon_t2star = apply_gridding(sparse_kspace_t2star)
recon_t2star_b0 = apply_gridding(sparse_kspace_t2star_b0)
recon_t2star_shimb0 = apply_gridding(sparse_kspace_t2star_shimb0)

# Calculate global min and max for consistent window leveling
global_min = min(np.min(recon_original), np.min(recon_t2star), np.min(recon_t2star_b0), np.min(recon_t2star_shimb0))
global_max = max(np.max(recon_original), np.max(recon_t2star), np.max(recon_t2star_b0), np.max(recon_t2star_shimb0))
print(f"Using global window level: vmin={global_min:.3f}, vmax={global_max:.3f}")

# Visualize results - updated to show 4 versions including shimmed B0
plt.figure(figsize=(20, 16))

# Row 1: Parameter maps
plt.subplot(4, 4, 1)
plt.imshow(t2star_plus_map, cmap='hot', vmin=0, vmax=100)
plt.colorbar()
plt.title('T2* Map (ms)')
plt.axis('off')

plt.subplot(4, 4, 2)
plt.imshow(b0_map, cmap='coolwarm', vmin=-50, vmax=50)
plt.colorbar()
plt.title('Original B0 Field Map (Hz)')
plt.axis('off')

plt.subplot(4, 4, 3)
plt.imshow(shimb0_map, cmap='coolwarm', vmin=-50, vmax=50)
plt.colorbar()
plt.title('Shimmed B0 Field Map (Hz)')
plt.axis('off')

plt.subplot(4, 4, 4)
# Create a cardiac mask visualization
# cardiac_mask_1 = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
# for label in [1, 2, 5, 6]:  # Cardiac structures
#     cardiac_mask_1 |= (data['Labels'][:,:,z_slice] == label)
# overlay = np.zeros((*cardiac_mask_1.shape, 3))
# for label, color in [(1, [1, 0, 0]),      # LV wall - red
#                     (2, [0, 0, 1]),       # RV wall - blue
#                     (5, [1, 0.7, 0.7]),   # LV blood - light red
#                     (6, [0.7, 0.7, 1])]:  # RV blood - light blue
#     overlay[data['Labels'][:,:,z_slice] == label] = color
plt.imshow(overlay)
plt.title('Cardiac Anatomy')
plt.axis('off')

# Row 2: Reconstructed images
plt.subplot(4, 4, 5)
plt.imshow(recon_original, cmap='gray', vmin=global_min, vmax=global_max)
plt.title('Spiral Recon (No Decay)')
plt.axis('off')

plt.subplot(4, 4, 6)
plt.imshow(recon_t2star, cmap='gray', vmin=global_min, vmax=global_max)
plt.title('With T2* Decay Only')
plt.axis('off')

plt.subplot(4, 4, 7)
plt.imshow(recon_t2star_b0, cmap='gray', vmin=global_min, vmax=global_max)
plt.title('With T2* and Original B0')
plt.axis('off')

plt.subplot(4, 4, 8)
plt.imshow(recon_t2star_shimb0, cmap='gray', vmin=global_min, vmax=global_max)
plt.title('With T2* and Shimmed B0')
plt.axis('off')

# Row 3: Difference maps compared to original
plt.subplot(4, 4, 9)
diff_t2star = recon_original - recon_t2star
plt.imshow(diff_t2star, cmap='hot', vmin=0)
plt.colorbar()
plt.title('Signal Loss from T2* Only')
plt.axis('off')

plt.subplot(4, 4, 10)
diff_t2star_b0 = recon_original - recon_t2star_b0
plt.imshow(diff_t2star_b0, cmap='hot', vmin=0)
plt.colorbar()
plt.title('Signal Loss from T2* + Original B0')
plt.axis('off')

plt.subplot(4, 4, 11)
diff_t2star_shimb0 = recon_original - recon_t2star_shimb0
plt.imshow(diff_t2star_shimb0, cmap='hot', vmin=0)
plt.colorbar()
plt.title('Signal Loss from T2* + Shimmed B0')
plt.axis('off')

plt.subplot(4, 4, 12)
b0_improvement = diff_t2star_b0 - diff_t2star_shimb0
plt.imshow(b0_improvement, cmap='hot', vmin=0)
plt.colorbar()
plt.title('Signal Recovery from Shimming')
plt.axis('off')

# Row 4: Focused comparisons
plt.subplot(4, 4, 13)
diff_b0 = recon_t2star - recon_t2star_b0
plt.imshow(diff_b0, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.colorbar()
plt.title('Effect of Original B0')
plt.axis('off')

plt.subplot(4, 4, 14)
diff_shimb0 = recon_t2star - recon_t2star_shimb0
plt.imshow(diff_shimb0, cmap='coolwarm', vmin=-0.1, vmax=0.1)
plt.colorbar()
plt.title('Effect of Shimmed B0')
plt.axis('off')

plt.subplot(4, 4, 15)
b0_diff = b0_map - shimb0_map
plt.imshow(b0_diff, cmap='coolwarm')
plt.colorbar()
plt.title('B0 Field Difference\n(Original - Shimmed)')
plt.axis('off')

plt.subplot(4, 4, 16)
diff_improvement = np.abs(diff_b0) - np.abs(diff_t2star_shimb0)
plt.imshow(diff_improvement, cmap='hot', vmin=0)
plt.colorbar()
plt.title('Absolute Improvement\nfrom Shimming')
plt.axis('off')

plt.tight_layout()
plt.show()

# Additional visualization: Signal decay profiles
plt.figure(figsize=(20, 5))


# Calculate radius from spiral coordinates
r = np.sqrt(kx_spiral**2 + ky_spiral**2)
# Plot k-space signal magnitude vs. radius
r_norm = r / r_max  # Normalized radius
r_order = np.argsort(r_norm)  # Sort by distance from center

# Calculate signal ratios
signal_original = np.abs(spiral_kspace_original)
signal_t2star = np.abs(spiral_kspace_t2star)
signal_t2star_b0 = np.abs(spiral_kspace_t2star_b0)
signal_t2star_shimb0 = np.abs(spiral_kspace_t2star_shimb0)

# Normalize to original signal
ratio_t2star = signal_t2star / (signal_original + 1e-10)
ratio_t2star_b0 = signal_t2star_b0 / (signal_original + 1e-10)
ratio_t2star_shimb0 = signal_t2star_shimb0 / (signal_original + 1e-10)


# Plot decay rates
plt.subplot(1, 4, 1)
plt.scatter(r_norm[r_order], ratio_t2star[r_order], 
           c='blue', s=3, alpha=0.3, label='T2* Only')
plt.scatter(r_norm[r_order], ratio_t2star_b0[r_order], 
           c='red', s=3, alpha=0.3, label='T2* + Original B0')
plt.scatter(r_norm[r_order], ratio_t2star_shimb0[r_order], 
           c='green', s=3, alpha=0.3, label='T2* + Shimmed B0')

# Add theoretical decay curve
typical_t2star = np.median(t2star_map) / 1000.0  # Convert to seconds
x_theory = np.linspace(0, 1, 100)
expected_decay = np.exp(-x_theory * readout_duration / typical_t2star)
plt.plot(x_theory, expected_decay, 'k--', linewidth=2, label='Theoretical')

plt.xlabel('Normalized k-space Distance')
plt.ylabel('Signal Ratio')
plt.title('Signal Decay vs. k-space Distance')
plt.grid(True, alpha=0.3)
plt.legend()
plt.ylim([0, 1.1])

# Show zoomed cardiac regions for all variants
# Find cardiac center for zooming
if np.any(cardiac_mask):
    y_indices, x_indices = np.where(cardiac_mask)
    center_y, center_x = int(np.mean(y_indices)), int(np.mean(x_indices))
else:
    center_y, center_x = image_size // 2, image_size // 2

# Define zoom region centered on cardiac area
zoom_size = 40
zoom_y_min = max(0, center_y - zoom_size)
zoom_y_max = min(image_size, center_y + zoom_size)
zoom_x_min = max(0, center_x - zoom_size)
zoom_x_max = min(image_size, center_x + zoom_size)

# Original
plt.subplot(1, 4, 2)
plt.imshow(recon_original[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title('Original (No Decay)')
plt.axis('off')

# With original B0
plt.subplot(1, 4, 3)
plt.imshow(recon_t2star_b0[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title('With T2* + Original B0')
plt.axis('off')

# With shimmed B0
plt.subplot(1, 4, 4)
plt.imshow(recon_t2star_shimb0[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title('With T2* + Shimmed B0')
plt.axis('off')

plt.tight_layout()
plt.show()

# Calculate statistics for all versions
print("\nSimulation Results Summary:")
print(f"T2* map range: {np.min(t2star_map):.1f} - {np.max(t2star_map):.1f} ms")
print(f"Original B0 field range: {np.min(b0_map):.1f} - {np.max(b0_map):.1f} Hz")
print(f"Shimmed B0 field range: {np.min(shimb0_map):.1f} - {np.max(shimb0_map):.1f} Hz")
print(f"B0 homogeneity improvement: {(1 - np.std(shimb0_map)/np.std(b0_map))*100:.1f}%")
print(f"Average signal loss from T2* decay: {100*np.mean(diff_t2star)/np.mean(recon_original):.1f}%")
print(f"Average signal loss with original B0: {100*np.mean(diff_t2star_b0)/np.mean(recon_original):.1f}%")
print(f"Average signal loss with shimmed B0: {100*np.mean(diff_t2star_shimb0)/np.mean(recon_original):.1f}%")
print(f"Signal recovery from shimming: {100*(np.mean(diff_t2star_b0) - np.mean(diff_t2star_shimb0))/np.mean(recon_original):.1f}%")

# Phase error analysis for both B0 fields
phase_orig = np.angle(orig_image)
phase_b0 = np.angle(orig_image * np.exp(-1j * 2 * np.pi * b0_map * readout_duration))
phase_shimb0 = np.angle(orig_image * np.exp(-1j * 2 * np.pi * shimb0_map * readout_duration))

phase_diff_orig = phase_b0 - phase_orig
phase_diff_shim = phase_shimb0 - phase_orig

print(f"Maximum phase error from original B0: {np.max(np.abs(phase_diff_orig)):.2f} radians ({np.max(np.abs(phase_diff_orig))*180/np.pi:.1f} degrees)")
print(f"Maximum phase error from shimmed B0: {np.max(np.abs(phase_diff_shim)):.2f} radians ({np.max(np.abs(phase_diff_shim))*180/np.pi:.1f} degrees)")
print(f"Phase error reduction: {(1 - np.max(np.abs(phase_diff_shim))/np.max(np.abs(phase_diff_orig)))*100:.1f}%")

def build_weight_map_and_A(kx_pix, ky_pix, N, width=1.1, beta=13.0):
    rows, cols, vals = [], [], []
    weight_map = []
    for i,(xc,yc) in enumerate(zip(kx_pix, ky_pix)):
        local = []
        xm, xM = max(0,int(xc-width)), min(N-1,int(xc+width))
        ym, yM = max(0,int(yc-width)), min(N-1,int(yc+width))
        for y in range(ym, yM+1):
            for x in range(xm, xM+1):
                d = np.hypot(x-xc, y-yc)
                w = kaiser_bessel_kernel(d, width, beta)
                if w>0:
                    local.append((y,x,w))
        weight_map.append(local)
        for (y,x,w) in local:
            rows.append(i); cols.append(y*N+x); vals.append(w)
    A = spr.csr_matrix((vals,(rows,cols)), shape=(len(weight_map), N*N))
    return weight_map, A

def reconstruct_from_samples(spiral_samples, weight_map):
    # spiral_samples: 1-d 长度 = len(weight_map)
    b = np.array([
        sum(w * spiral_samples[i] for (_,_,w) in weight_map[i])
        for i in range(len(weight_map))
    ], dtype=complex)
    sol = lsqr(A, b, damp=λ, iter_lim=10000)[0]
    return sol.reshape(image_size, image_size)


def reconstruct_with_A(A, weight_map, spiral_samples, N, lam=1e-6):
    # 构造 b 向量
    b = np.empty(len(weight_map), dtype=complex)
    for i, local in enumerate(weight_map):
        W = sum(w for (_,_,w) in local)
        if W > 0:
            b[i] = sum(w * spiral_samples[i] for (_,_,w) in local) / W
        else:
            b[i] = 0+0j
    sol = lsqr(A, b, damp=lam, iter_lim=10000)[0]
    return sol.reshape(N, N)

def forward_gridding(weight_map, kspace, N):
    """从笛卡尔 k-space，通过 weight_map 插值得到 Nk 长度的 spiral samples"""
    Nk = len(weight_map)
    spiral = np.zeros(Nk, dtype=complex)
    for i, local in enumerate(weight_map):
        W = sum(w for (_,_,w) in local)
        if W>0:
            spiral[i] = sum(w * kspace[y,x] for (y,x,w) in local) / W
    return spiral

import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d

# Use output from T2* decay simulation as input
print("Adding gradient delay effects to spiral trajectory with T2* decay")

# First, check if we have the necessary variables from previous cells
required_vars = ['kx_spiral', 'ky_spiral', 'sampling_times', 'spiral_kspace_t2star_b0']

# Calculate k_max if not already defined
if 'k_max' not in locals():
    k_max = 0.5 * image_size / fov
    print(f"Calculated k_max: {k_max:.2f} cycles/m")

def apply_gradient_delays_corrected(kx_spiral, ky_spiral, sampling_times, delay_x=0.0, delay_y=0.0, amplitude_nonlinearity=0.0):
    """Apply realistic gradient timing delays to spiral k-space trajectory"""
    # Convert delays from ms to same time units as sampling_times
    delay_x_norm = delay_x * 1e-3  # Convert ms to seconds
    delay_y_norm = delay_y * 1e-3
    
    # Create interpolation functions for continuous trajectory sampling
    from scipy.interpolate import interp1d
    
    # Create separate interpolators for X and Y trajectories
    kx_interp = interp1d(sampling_times, kx_spiral, bounds_error=False, fill_value="extrapolate")
    ky_interp = interp1d(sampling_times, ky_spiral, bounds_error=False, fill_value="extrapolate")
    
    # Delayed time points for sampling
    t_delayed_x = sampling_times - delay_x_norm
    t_delayed_y = sampling_times - delay_y_norm
    
    # Sample the trajectories at the delayed times
    kx_delayed = kx_interp(t_delayed_x)
    ky_delayed = ky_interp(t_delayed_y)
    
    # Apply amplitude-dependent nonlinearity if specified
    if amplitude_nonlinearity > 0:
        # Calculate gradients (proportional to gradient strength)
        dkx_dt = np.gradient(kx_spiral, sampling_times)
        dky_dt = np.gradient(ky_spiral, sampling_times)
        
        # Calculate delayed gradients
        dkx_delayed_dt = np.gradient(kx_delayed, sampling_times)
        dky_delayed_dt = np.gradient(ky_delayed, sampling_times)
        
        # Scale nonlinearity effect by gradient strength
        max_grad = max(np.max(np.abs(dkx_dt)), np.max(np.abs(dky_dt)))
        scale_factor = amplitude_nonlinearity / max_grad if max_grad > 0 else 0
        
        # Add nonlinear distortion proportional to gradient amplitude
        kx_delayed += scale_factor * dkx_delayed_dt**2 * np.sign(dkx_delayed_dt)
        ky_delayed += scale_factor * dky_delayed_dt**2 * np.sign(dky_delayed_dt)
    
    return kx_delayed, ky_delayed

# Use the spiral trajectory with T2* and B0 effects as the baseline
# Choose which T2* effect version to use
spiral_kspace_t2star_final = spiral_kspace_t2star_b0  # Use version with both T2* and B0 effects
print(f"Using k-space data with T2* and B0 effects as baseline")

# Apply gradient delays to the spiral trajectory
# Typical delay values in clinical scanners: 5-50 μs
# Mild imperfection (high-end system)
delay_x = 0  # μs
delay_y = 0  # μs

# # Moderate imperfection (typical clinical system)
# delay_x = 15.0  # μs
# delay_y = 18.0  # μs

# # # Severe imperfection (poorly calibrated system)
# delay_x = 30.0  # μs
# delay_y = 35.0  # μs

nonlinearity = 1e-6

# Convert from microseconds to milliseconds for the function
kx_spiral_delayed, ky_spiral_delayed = apply_gradient_delays_corrected(
    kx_spiral, 
    ky_spiral, 
    sampling_times,
    delay_x=delay_x/1000.0,  # Convert to ms
    delay_y=delay_y/1000.0,  # Convert to ms
    amplitude_nonlinearity=nonlinearity
)

# Visualize the original vs delayed trajectories
plt.figure(figsize=(12, 6))

plt.subplot(1, 2, 1)
plt.plot(kx_spiral, ky_spiral, 'b-', linewidth=1, label='Ideal')
plt.plot(kx_spiral_delayed, ky_spiral_delayed, 'r-', linewidth=1, label='Delayed')
plt.axis('equal')
plt.title("Full Spiral Trajectory")
plt.legend()
plt.grid(True)

# Zoom in on a small section to see delay effects
plt.subplot(1, 2, 2)
# Select just a small portion (e.g., 5%) of the trajectory for zoomed view
zoom_range = int(len(kx_spiral) * 0.05)
start_idx = int(len(kx_spiral) * 0.3)  # Start at 30% into trajectory
plt.plot(kx_spiral[start_idx:start_idx+zoom_range], 
         ky_spiral[start_idx:start_idx+zoom_range], 'b-', linewidth=2, label='Ideal')
plt.plot(kx_spiral_delayed[start_idx:start_idx+zoom_range], 
         ky_spiral_delayed[start_idx:start_idx+zoom_range], 'r-', linewidth=2, label='Delayed')
plt.axis('equal')
plt.title("Zoomed View of Gradient Delay Effects")
plt.legend()
plt.grid(True)

plt.tight_layout()
plt.show()

# Reconstruction start from here

kx_pix_del = (kx_spiral_delayed / k_max) * (image_size/2) + image_size/2
ky_pix_del = (ky_spiral_delayed / k_max) * (image_size/2) + image_size/2
wm_del, A_del = build_weight_map_and_A(kx_pix_del, ky_pix_del,
                                               image_size,
                                               kernel_width_pixels,
                                               kernel_beta)

# spiral_delayed = forward_gridding(wm_del,   kspace, image_size)

# Fix: Use the delayed trajectory to grid the k-space data directly
# The spiral_kspace_t2star_final contains the original spiral samples
# We need to grid these using the delayed trajectory coordinates
spiral_samples_input = spiral_kspace_t2star_final

lambda_reg = 1e-6
sparse_delayed = reconstruct_with_A(A_del, wm_del, spiral_samples_input, lambda_reg)

    

def grid_spiral_to_cartesian(spiral_kspace, weight_map, image_size):
    """Grid spiral k-space data to Cartesian coordinates using weight map"""
    sparse_kspace = np.zeros((image_size, image_size), dtype=complex)
    weight_grid = np.zeros((image_size, image_size), dtype=float)
    
    for i, local_weights in enumerate(weight_map):
        for y, x, weight in local_weights:
            sparse_kspace[y, x] += weight * spiral_kspace[i]
            weight_grid[y, x] += weight
    
    # Normalize by weights
    mask = weight_grid > 0
    sparse_kspace[mask] /= weight_grid[mask]
    
    return sparse_kspace    


sparse_kspace_delayed = grid_spiral_to_cartesian(spiral_samples_input, wm_del, image_size)

# Reconstruct image with delayed trajectory
# Convert delayed k-space coordinates to pixel indices
# kx_pixel_delayed = ((kx_spiral_delayed / k_max) * (image_size/2) + image_size/2).astype(int)
# ky_pixel_delayed = ((ky_spiral_delayed / k_max) * (image_size/2) + image_size/2).astype(int)

# # Clip to valid range
# kx_pixel_delayed = np.clip(kx_pixel_delayed, 0, image_size-1)
# ky_pixel_delayed = np.clip(ky_pixel_delayed, 0, image_size-1)


# Sample k-space using delayed coordinates
# sparse_kspace_delayed = np.zeros((image_size, image_size), dtype=complex)
# sparse_kspace_delayed[ky_pixel_delayed, kx_pixel_delayed] = spiral_kspace_t2star_final

# Apply gridding reconstruction (simple Gaussian smoothing for demonstration)
def apply_gridding(sparse_kspace):
    real_part = gaussian_filter(sparse_kspace.real, sigma=0.3)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=0.3)
    smoothed_kspace = real_part + 1j * imag_part
    # recon = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(smoothed_kspace)))
    recon = np.fft.ifft2(np.fft.ifftshift(smoothed_kspace))
    return np.abs(recon)

def apply_gridding_and_display(sparse_kspace, N, apod=None):
    # 这里演示用简单 Gaussian 平滑再 FFT
    real_part = gaussian_filter(sparse_kspace.real, sigma=0.1)
    imag_part = gaussian_filter(sparse_kspace.imag, sigma=0.1)
    sk = real_part + 1j*imag_part
    img = np.fft.ifft2(np.fft.ifftshift(sk))
    mag = np.abs(img)
    if apod is not None:
        mag /= (apod + 1e-6)
    plt.imshow(mag, cmap='gray')
    plt.axis('off')
    return mag

# Reconstruct the image with gradient delays
recon_delayed = apply_gridding_and_display(sparse_delayed, image_size)

# For comparison, reconstruct the non-delayed image
# sparse_kspace_ideal = np.zeros((image_size, image_size), dtype=complex)
# sparse_kspace_ideal[ky_pixel, kx_pixel] = spiral_kspace_t2star_final


sparse_kspace_ideal   = reconstruct_from_samples(spiral_kspace_t2star_final,weight_map)

recon_ideal = apply_gridding(sparse_kspace_ideal)

# Compare the reconstructions with zoom
plt.figure(figsize=(15, 10))

# Find center of cardiac region for better zooming
# 修复：先检查z_slice是否存在，如果不存在就定义它
if 'z_slice' not in locals():
    z_slice = data['Labels'].shape[2] // 2  # 使用中间切片

# 修复：安全地创建心脏遮罩
try:
    cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
    for label in [1, 2, 5, 6]:  # Cardiac structures
        cardiac_mask |= (data['Labels'][:,:,z_slice] == label)
except:
    # 如果上面的方法失败，创建一个简单的中心区域遮罩
    cardiac_mask = np.zeros((image_size, image_size), dtype=bool)
    center = image_size // 2
    cardiac_mask[center-20:center+20, center-20:center+20] = True

# # Find center of cardiac region for better zooming
# cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
# for label in [1, 2, 5, 6]:  # Cardiac structures
#     cardiac_mask |= (data['Labels'][:,:,z_slice] == label)

if np.any(cardiac_mask):
    y_indices, x_indices = np.where(cardiac_mask)
    zoom_y, zoom_x = int(np.mean(y_indices)), int(np.mean(x_indices))
else:
    zoom_y, zoom_x = image_size // 2, image_size // 2

# Define zoom parameters around cardiac center
zoom_size = min(60, image_size // 3)  # Adjust based on your image size
zoom_rows = slice(max(0, zoom_y-zoom_size), min(image_size, zoom_y+zoom_size))
zoom_cols = slice(max(0, zoom_x-zoom_size), min(image_size, zoom_x+zoom_size))

# First row: Full images
# Without delays
plt.subplot(2, 3, 1)
plt.imshow(recon_ideal, cmap='gray')
plt.title("T2*/B0 Only (No Delays)")
plt.colorbar()
# Add ROI box
rect = plt.Rectangle((zoom_cols.start, zoom_rows.start), 
                     zoom_cols.stop-zoom_cols.start, zoom_rows.stop-zoom_rows.start, 
                     edgecolor='r', facecolor='none', linewidth=2)
plt.gca().add_patch(rect)

# With gradient delays
plt.subplot(2, 3, 2)
plt.imshow(recon_delayed, cmap='gray')
plt.title(f"With Gradient Delays\nX: {delay_x}μs, Y: {delay_y}μs")
plt.colorbar()
# Add ROI box
rect = plt.Rectangle((zoom_cols.start, zoom_rows.start), 
                     zoom_cols.stop-zoom_cols.start, zoom_rows.stop-zoom_rows.start, 
                     edgecolor='r', facecolor='none', linewidth=2)
plt.gca().add_patch(rect)

# Difference image
plt.subplot(2, 3, 3)
plt.imshow(np.abs(recon_delayed - recon_ideal), cmap='hot')
plt.title("Difference (Delay Artifacts)")
plt.colorbar()
# Add ROI box
rect = plt.Rectangle((zoom_cols.start, zoom_rows.start), 
                     zoom_cols.stop-zoom_cols.start, zoom_rows.stop-zoom_rows.start, 
                     edgecolor='r', facecolor='none', linewidth=2)
plt.gca().add_patch(rect)

# Second row: Zoomed images
# Zoomed without delays
plt.subplot(2, 3, 4)
plt.imshow(recon_ideal[zoom_rows, zoom_cols], cmap='gray')
plt.title("T2*/B0 Only (Zoomed)")
plt.colorbar()

# Zoomed with gradient delays
plt.subplot(2, 3, 5)
plt.imshow(recon_delayed[zoom_rows, zoom_cols], cmap='gray')
plt.title(f"With Delays (Zoomed)")
plt.colorbar()

# Zoomed difference
plt.subplot(2, 3, 6)
plt.imshow(np.abs(recon_delayed - recon_ideal)[zoom_rows, zoom_cols], cmap='hot')
plt.title("Difference (Zoomed)")
plt.colorbar()

plt.tight_layout()
plt.show()

# Add line profile comparison
plt.figure(figsize=(15, 5))

# Plot a horizontal line through the center of the ROI
profile_y = zoom_y

# Plot the images with the profile line
plt.subplot(1, 3, 1)
plt.imshow(recon_ideal, cmap='gray')
plt.axhline(y=profile_y, color='r', linestyle='-')
plt.title("T2*/B0 Only with Profile Line")

plt.subplot(1, 3, 2)
plt.imshow(recon_delayed, cmap='gray')
plt.axhline(y=profile_y, color='r', linestyle='-')
plt.title("Delayed with Profile Line")

# Extract and plot the profiles
profile_ideal = recon_ideal[profile_y, :]
profile_delayed = recon_delayed[profile_y, :]

plt.subplot(1, 3, 3)
plt.plot(profile_ideal, 'b-', linewidth=1.5, label='No Delay')
plt.plot(profile_delayed, 'r-', linewidth=1.5, label='With Delay')
plt.legend()
plt.title("Signal Intensity Profiles")
plt.xlabel("Position")
plt.ylabel("Signal Intensity")

plt.tight_layout()
plt.show()

# Add comparison of artifacts
plt.figure(figsize=(15, 5))
plt.subplot(1, 3, 1)
plt.imshow(recon_t2star_b0, cmap='gray')
plt.title("With T2* and B0 Effects")
plt.colorbar()

plt.subplot(1, 3, 2)
plt.imshow(recon_delayed, cmap='gray')
plt.title("With T2*, B0 and Gradient Delays")
plt.colorbar()

plt.subplot(1, 3, 3)
# Calculate RMSE differences to quantify artifacts
rmse_t2star = np.sqrt(np.mean((recon_original - recon_t2star)**2))
rmse_b0 = np.sqrt(np.mean((recon_t2star - recon_t2star_b0)**2))
rmse_delay = np.sqrt(np.mean((recon_ideal - recon_delayed)**2))

# Plot the relative artifact contributions
labels = ['T2* Decay', 'B0 Effects', 'Gradient Delays']
values = [rmse_t2star, rmse_b0, rmse_delay]
plt.bar(labels, values)
plt.title("Relative Artifact Contributions (RMSE)")
plt.ylabel("RMSE Difference")
plt.grid(axis='y')

plt.tight_layout()
plt.show()

print("\nArtifact Analysis:")
print(f"T2* decay artifacts RMSE: {rmse_t2star:.5f}")
print(f"B0 field effect artifacts RMSE: {rmse_b0:.5f}")
print(f"Gradient delay artifacts RMSE: {rmse_delay:.5f}")

delay_percent = (rmse_delay / (rmse_t2star + rmse_b0 + rmse_delay)) * 100
print(f"Gradient delays contribute {delay_percent:.1f}% of the total artifact level")

# Calculate required number of spiral turns based on Nyquist sampling
def calculate_spiral_turns(fov, resolution, interleaves=1):
    # Maximum k-space extent
    kmax = 1 / (2 * resolution)
    
    # Calculate circumference at maximum extent
    max_circumference = 2 * np.pi * kmax
    
    # Required samples based on Nyquist (1/FOV)
    required_samples_at_max = max_circumference * fov
    
    # Required turns (factoring in interleaves)
    required_turns = required_samples_at_max / interleaves
    
    return required_turns

# Calculate for our parameters
fov_xy = 150e-3  # 150mm
resolution = 2e-3  # 2mm
interleaves = 1

required_turns = calculate_spiral_turns(fov_xy, resolution, interleaves)
print(f"Required number of spiral turns: {required_turns:.1f}")

import pypulseq as pp
import numpy as np
import matplotlib.pyplot as plt

# Define system limits
system = pp.Opts(max_grad=200, grad_unit='mT/m', max_slew=200, slew_unit='mT/m/ms')

# Create sequence object
seq = pp.Sequence(system=system)

# Specific parameters
fov_xy = 150e-3       # FOV in meters
resolution = 2e-3     # 2mm resolution
n_slices = 1          # Start with fewer slices for testing
slice_thickness = 2e-3  # 2mm slice thickness
slice_gap = 0         # No gap between slices

# Calculate number of pixels based on FOV and resolution
nx = int(np.round(fov_xy / resolution))  # Number of samples in x direction
ny = int(np.round(fov_xy / resolution))  # Number of samples in y direction

print(f"Matrix size: {nx}x{ny}")

# Spiral parameters
dt = 2e-6             # Spiral sampling time in seconds
kmax = nx/(2*fov_xy)  # Maximum k-space value (1/m)
readout_time = 20e-3   # Spiral readout duration in seconds
samples = int(readout_time / dt)  # Number of samples

# Create spiral trajectory (Archimedean spiral)
t = np.linspace(0, readout_time, samples)
theta = 2 * np.pi * 236 * t / readout_time  # 236 spiral turns
r = kmax * t / readout_time  # Linear increase in radius

# Convert to Cartesian coordinates
kx = r * np.cos(theta)
ky = r * np.sin(theta)

# Calculate gradients (G = dk/dt * gamma)
gamma = 42.576e6  # Hz/T, gyromagnetic ratio for protons
gx = np.diff(kx) / dt * 2 * np.pi / gamma
gy = np.diff(ky) / dt * 2 * np.pi / gamma

# Add one point to have same array size
gx = np.append(gx, gx[-1])
gy = np.append(gy, gy[-1])

# Scale down gradients if they exceed limits
max_grad_Hz_per_m = system.max_grad * 1e-3 * gamma  # Convert from mT/m to Hz/m
g_max = max(np.max(np.abs(gx)), np.max(np.abs(gy)))
if g_max > max_grad_Hz_per_m:
    scale_factor = max_grad_Hz_per_m / g_max
    gx *= scale_factor
    gy *= scale_factor
    print(f"Gradients scaled down by factor {scale_factor:.3f} to meet system limits")

# Create arbitrary gradient objects
grad_x = pp.make_arbitrary_grad(channel='x', waveform=gx, system=system)
grad_y = pp.make_arbitrary_grad(channel='y', waveform=gy, system=system)

# Create ADC event with same number of samples as the spiral trajectory
adc = pp.make_adc(num_samples=samples,
                 duration=readout_time,
                 system=system)

delay = pp.make_delay(1e-3)

# Calculate and display ADC rate and bandwidth
adc_rate = samples / readout_time
adc_bandwidth = 1 / dt
print(f"ADC samples: {samples}")
print(f"ADC sampling rate: {adc_rate/1000:.2f} kHz")
print(f"ADC bandwidth: {adc_bandwidth/1000:.2f} kHz")

# Calculate and display total scan time
TR = 20e-3  # Repetition time
n_interleaves = 1     # Number of spiral interleaves (arms)
total_scan_time_single_interleave = n_slices * TR
total_scan_time_full = n_slices * n_interleaves * TR

print(f"Total scan time for {n_slices} slices with {n_interleaves} interleave(s): {total_scan_time_single_interleave*1000:.1f} ms")
print(f"Estimated full scan time for 150 slices with {n_interleaves} interleave(s): {150*n_interleaves*TR*1000:.1f} ms")

# For multiple interleaves, the rotation angle for each interleave
if n_interleaves > 1:
    rotation_angle = 2 * np.pi / n_interleaves
    print(f"Rotation angle between interleaves: {rotation_angle*180/np.pi:.1f} degrees")

# Full 3D acquisition with all slices - FIXED VERSION
for slice_idx in range(n_slices):
    # Calculate slice position (from -FOV/2 to FOV/2)
    slice_pos = (slice_idx - n_slices/2) * slice_thickness
    
    # Create slice selective RF pulse for this position
    # NOTE: Instead of using return_gz=True, we'll create the RF and gradients separately
    rf = pp.make_sinc_pulse(flip_angle=15 * np.pi / 180,
                          duration=2e-3,
                          system=system,
                          slice_thickness=slice_thickness,
                          apodization=0.5,
                          time_bw_product=4)
    
    # Create a slice selection gradient based on the slice position
    # The amplitude needs to be proportional to the slice offset
    amplitude = 1.0  # Base amplitude
    if slice_idx > 0:  # Adjust for off-center slices
        amplitude += slice_pos / (n_slices * slice_thickness) 
    
    # Create the slice selection gradient
    gz = pp.make_trapezoid(channel='z', 
                         system=system,
                         amplitude=amplitude,
                         duration=2e-3)
    
    # Add sequence blocks
    seq.add_block(rf, gz)
    seq.add_block(delay)
    seq.add_block(grad_x, grad_y, adc)
    
    # Add TR delay if needed
    remaining_time = TR - (pp.calc_duration(rf) + pp.calc_duration(delay) + readout_time)
    if remaining_time > 0:
        delay_TR = pp.make_delay(remaining_time)
        seq.add_block(delay_TR)

# Calculate sequence duration
duration_result = seq.duration()
# Check if duration is a tuple and extract the first value if it is
if isinstance(duration_result, tuple):
    duration = duration_result[0]  # Extract the first element of the tuple
else:
    duration = duration_result  # Use as is if it's not a tuple

print(f"Actual sequence duration: {duration*1000:.1f} ms")

# Similarly update the file writing part
# with open('spiral_sequence_info.txt', 'w') as f:
#     f.write(f"Spiral Sequence Parameters\n")
#     f.write(f"-------------------------\n")
#     # ...other lines...
#     f.write(f"Actual sequence duration: {duration*1000:.1f} ms\n")
#     f.write(f"Estimated full scan time (150 slices): {150*n_interleaves*TR*1000:.1f} ms\n")

# Write information to text file
with open('spiral_sequence_info.txt', 'w') as f:
    f.write(f"Spiral Sequence Parameters\n")
    f.write(f"-------------------------\n")
    f.write(f"FOV: {fov_xy*1000:.1f} mm x {fov_xy*1000:.1f} mm\n")
    f.write(f"Resolution: {resolution*1000:.1f} mm x {resolution*1000:.1f} mm\n")
    f.write(f"Matrix size: {nx}x{ny}\n")
    f.write(f"Slice thickness: {slice_thickness*1000:.1f} mm\n")
    f.write(f"Number of slices: {n_slices} (sample), 150 (full)\n")
    f.write(f"Number of interleaves: {n_interleaves}\n")
    f.write(f"Repetition time (TR): {TR*1000:.1f} ms\n")
    f.write(f"Readout time: {readout_time*1000:.1f} ms\n")
    f.write(f"ADC samples per spiral: {samples}\n")
    f.write(f"ADC sampling rate: {adc_rate/1000:.2f} kHz\n")
    f.write(f"ADC bandwidth: {adc_bandwidth/1000:.2f} kHz\n")
    f.write(f"Actual sequence duration: {duration*1000:.1f} ms\n")
    f.write(f"Estimated full scan time (150 slices): {150*n_interleaves*TR*1000:.1f} ms\n")

# Visualize sequence (only the first few blocks)
seq.plot(time_range=[0, 5*TR])  # Display first 5 TRs
plt.savefig('spiral_sequence_plot.png')
plt.show()

# Plot the k-space trajectory
plt.figure()
plt.plot(kx, ky)
plt.title('Spiral K-space trajectory')
plt.xlabel('kx (1/m)')
plt.ylabel('ky (1/m)')
plt.axis('equal')
plt.grid(True)
plt.savefig('spiral_kspace.png')
plt.show()

# Write sequence to file
# seq.write('spiral_sequence_3d.seq')

import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter

# Extract spiral trajectory from PyPulseq sequence (from previous cells)
print("Using spiral trajectory from PyPulseq sequence")
print(f"Spiral parameters: {samples} samples, {readout_time*1000:.1f}ms readout")

# Convert the PyPulseq k-space trajectory to match our image dimensions
# Get input T2*+ GRE image and convert to k-space
image_data = gre_t2star_plus_image
image_size = image_data.shape[0]  # Should be 150
kspace_full = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(image_data)))
print(f"Input image shape: {image_data.shape}")
print(f"Full k-space shape: {kspace_full.shape}")

# Scale the PyPulseq trajectory (in 1/m) to match our image pixel dimensions
k_scaling = (image_size/2) / kmax  # Scale factor from 1/m to pixels
kx_pixel = kx * k_scaling + image_size/2  # Shift to center
ky_pixel = ky * k_scaling + image_size/2
print(f"Trajectory points: {len(kx_pixel)}")

# Round to nearest pixel and clip to valid range
kx_pixel_int = np.clip(np.round(kx_pixel).astype(int), 0, image_size-1)
ky_pixel_int = np.clip(np.round(ky_pixel).astype(int), 0, image_size-1)

# Create a sampling mask from the spiral trajectory
sampling_mask = np.zeros((image_size, image_size), dtype=bool)
for i in range(len(kx_pixel_int)):
    x, y = kx_pixel_int[i], ky_pixel_int[i]
    sampling_mask[y, x] = True

# Count coverage
coverage = np.sum(sampling_mask) / (image_size * image_size) * 100
print(f"k-space coverage: {coverage:.2f}% of pixels")

# Calculate density compensation function (DCF) - radial distance based
r_norm = np.sqrt((kx/kmax)**2 + (ky/kmax)**2)  # Normalized radius [0,1]
dcf = np.sqrt(r_norm + 0.1)  # Simple DCF with small offset to avoid zero at center

# ===== Generate synthetic coil sensitivity maps =====
def generate_coil_sensitivity_maps(image_size, n_coils=8, coil_radius=1.2):
    """Generate synthetic coil sensitivity maps with rectangular FOV"""
    maps = np.zeros((n_coils, image_size, image_size), dtype=complex)
    
    # Create a grid of coordinates
    x = np.linspace(-1, 1, image_size)
    y = np.linspace(-1, 1, image_size)
    X, Y = np.meshgrid(x, y)
    
    # Define coil positions around a rectangular FOV (match image dimensions)
    # Use an elliptical layout to better match rectangular FOV
    aspect_ratio = 1.0  # Adjust if your FOV isn't square
    theta = np.linspace(0, 2*np.pi, n_coils, endpoint=False)
    coil_x = coil_radius * np.cos(theta)
    coil_y = coil_radius * aspect_ratio * np.sin(theta)
    
    # Generate sensitivity profile for each coil
    for i in range(n_coils):
        # Distance from each pixel to the coil
        distance = np.sqrt((X - coil_x[i])**2 + (Y - coil_y[i])**2)
        
        # Sensitivity falls off with distance (Gaussian model)
        sensitivity = np.exp(-distance**2 / (2 * 0.7**2))
        
        # Add some phase variation
        phase = np.angle(X + 1j*Y) * 0.3 - theta[i]
        complex_sensitivity = sensitivity * np.exp(1j * phase)
        
        # Apply a rectangular body mask to match FOV shape
        body_mask = (np.abs(X) <= 0.9) & (np.abs(Y) <= 0.9)
        maps[i, body_mask] = complex_sensitivity[body_mask]
        
        # Smooth the sensitivity map
        maps[i] = gaussian_filter(maps[i].real, sigma=0.8) + 1j * gaussian_filter(maps[i].imag, sigma=0.8)
    
    # Normalize the combined sensitivity maps
    # This ensures consistent brightness across different coil counts
    sum_of_squares = np.sqrt(np.sum(np.abs(maps)**2, axis=0))
    valid_mask = sum_of_squares > 0
    
    for i in range(n_coils):
        maps[i, valid_mask] = maps[i, valid_mask] / sum_of_squares[valid_mask]
    
    return maps

# Generate coil sensitivity maps for different numbers of coils
coil_configs = [4, 8, 16, 32]
all_sensitivity_maps = {}
for n_coils in coil_configs:
    all_sensitivity_maps[n_coils] = generate_coil_sensitivity_maps(image_size, n_coils)
    print(f"Generated {n_coils}-channel coil sensitivity maps")

# Visualize the coil sensitivity maps for the 8-coil configuration
plt.figure(figsize=(15, 8))
n_coils_to_show = 8
sensitivity_maps = all_sensitivity_maps[n_coils_to_show]
for i in range(n_coils_to_show):
    plt.subplot(2, 4, i+1)
    plt.imshow(np.abs(sensitivity_maps[i]), cmap='viridis')
    plt.title(f"Coil {i+1} Sensitivity")
    plt.axis('off')
plt.tight_layout()
plt.show()

# ===== Generate undersampled multi-coil k-space data =====
def generate_multicoil_kspace(image, sensitivity_maps, sampling_mask=None):
    """Generate multi-coil k-space data with optional undersampling"""
    n_coils = sensitivity_maps.shape[0]
    size_x, size_y = image.shape
    
    # Apply coil sensitivity maps to generate individual coil images
    coil_images = np.zeros((n_coils, size_x, size_y), dtype=complex)
    for i in range(n_coils):
        coil_images[i] = image * sensitivity_maps[i]
    
    # Transform to k-space
    coil_kspace = np.zeros_like(coil_images)
    for i in range(n_coils):
        coil_kspace[i] = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(coil_images[i])))
    
    # Apply sampling mask if provided
    if sampling_mask is not None:
        for i in range(n_coils):
            coil_kspace[i] *= sampling_mask
    
    return coil_kspace, coil_images

# ===== Implement SENSE reconstruction =====
def sense_reconstruction(undersampled_kspace, sensitivity_maps, sampling_mask, dcf=None, lambda_reg=1e-3):
    """
    SENSE reconstruction for parallel imaging with arbitrary sampling
    
    Parameters:
    -----------
    undersampled_kspace : ndarray [n_coils, nx, ny]
        Undersampled k-space data from each coil
    sensitivity_maps : ndarray [n_coils, nx, ny]
        Coil sensitivity maps
    sampling_mask : ndarray [nx, ny]
        Binary mask indicating sampled k-space points
    dcf : ndarray [nx, ny] or None
        Density compensation function
    lambda_reg : float
        Regularization parameter for stabilizing the reconstruction
    
    Returns:
    --------
    recon_image : ndarray [nx, ny]
        Reconstructed image
    """
    n_coils, nx, ny = undersampled_kspace.shape
    
    # Apply density compensation if provided
    if dcf is not None:
        for c in range(n_coils):
            undersampled_kspace[c] *= sampling_mask * dcf
    
    # Gridding - apply Gaussian smoothing to k-space
    gridded_kspace = np.zeros_like(undersampled_kspace)
    for c in range(n_coils):
        gridded_kspace[c] = gaussian_filter(undersampled_kspace[c].real, sigma=0.5) + \
                           1j * gaussian_filter(undersampled_kspace[c].imag, sigma=0.5)
    
    # Transform to image domain
    coil_images = np.zeros_like(gridded_kspace)
    for c in range(n_coils):
        coil_images[c] = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(gridded_kspace[c])))
    
    # Combine coil images using sensitivity maps (SENSE)
    recon_image = np.zeros((nx, ny), dtype=complex)
    
    # Use matrix operations for faster reconstruction and better normalization
    sensitivity_sum = np.zeros((nx, ny), dtype=float)
    
    # For each pixel, solve the SENSE equation
    for x in range(nx):
        for y in range(ny):
            # Create encoding matrix for this pixel [n_coils × 1]
            E = sensitivity_maps[:, x, y].reshape(n_coils, 1)
            
            # Coil measurements for this pixel
            m = coil_images[:, x, y].reshape(n_coils, 1)
            
            # Check if we have any signal at this pixel
            if np.sum(np.abs(E)) > 0:
                # Use Tikhonov regularized solution
                EH = E.conj().T
                regularized_matrix = EH @ E + lambda_reg * np.eye(1)
                solution = np.linalg.solve(regularized_matrix, EH @ m)
                recon_image[x, y] = solution.item(0)  # Fixed: explicitly extract scalar
                
                # Track total sensitivity for normalization
                sensitivity_sum[x, y] = np.sum(np.abs(E))
    
    # Apply normalization to account for different coil counts
    # This ensures consistent brightness across reconstructions
    valid_mask = sensitivity_sum > 0
    recon_image[valid_mask] /= (sensitivity_sum[valid_mask] / n_coils)
    
    return recon_image

# ===== Generate a multi-coil DCF for spiral =====
def apply_multicoil_dcf_to_kspace(kspace, kx_pixel_int, ky_pixel_int, dcf):
    """Apply density compensation to each point in the spiral trajectory"""
    n_coils, nx, ny = kspace.shape
    kspace_with_dcf = kspace.copy()
    
    for c in range(n_coils):
        for i in range(len(kx_pixel_int)):
            x, y = kx_pixel_int[i], ky_pixel_int[i]
            kspace_with_dcf[c, y, x] = kspace[c, y, x] * dcf[i]
            
    return kspace_with_dcf

# Create the DCF array for the k-space sampling pattern
dcf_map = np.zeros((image_size, image_size))
for i in range(len(kx_pixel_int)):
    x, y = kx_pixel_int[i], ky_pixel_int[i]
    dcf_map[y, x] = dcf[i]

# Compare reconstructions with different numbers of coils
results = {}
for n_coils in coil_configs:
    sensitivity_maps = all_sensitivity_maps[n_coils]
    
    # Generate multi-coil k-space data
    multicoil_kspace, multicoil_images = generate_multicoil_kspace(image_data, sensitivity_maps)
    
    # Apply spiral sampling mask - keep only the points on the trajectory
    sparse_multicoil_kspace = np.zeros_like(multicoil_kspace)
    for c in range(n_coils):
        for i in range(len(kx_pixel_int)):
            x, y = kx_pixel_int[i], ky_pixel_int[i]
            sparse_multicoil_kspace[c, y, x] = multicoil_kspace[c, y, x]
    
    # Apply density compensation to sparse k-space
    sparse_multicoil_kspace = apply_multicoil_dcf_to_kspace(
        sparse_multicoil_kspace, kx_pixel_int, ky_pixel_int, dcf
    )
    
    # Perform SENSE reconstruction
    reconstructed_sense = sense_reconstruction(
        sparse_multicoil_kspace, sensitivity_maps, sampling_mask, None, lambda_reg=1e-3
    )
    
    # Simple sum-of-squares reconstruction for comparison
    gridded_kspace = np.zeros_like(sparse_multicoil_kspace)
    for c in range(n_coils):
        gridded_kspace[c] = gaussian_filter(sparse_multicoil_kspace[c].real, sigma=0.5) + \
                           1j * gaussian_filter(sparse_multicoil_kspace[c].imag, sigma=0.5)
    
    sos_images = np.zeros_like(sparse_multicoil_kspace, dtype=complex)
    for c in range(n_coils):
        sos_images[c] = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(gridded_kspace[c])))
    
    reconstructed_sos = np.sqrt(np.sum(np.abs(sos_images)**2, axis=0))
    
    # Calculate error metrics
    mse_sense = np.mean(np.abs(image_data - np.abs(reconstructed_sense))**2)
    psnr_sense = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sense))
    
    mse_sos = np.mean(np.abs(image_data - reconstructed_sos)**2)
    psnr_sos = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sos))
    
    results[n_coils] = {
        'sense': np.abs(reconstructed_sense),
        'sos': reconstructed_sos,
        'mse_sense': mse_sense,
        'psnr_sense': psnr_sense,
        'mse_sos': mse_sos,
        'psnr_sos': psnr_sos
    }
    
    print(f"{n_coils}-channel reconstruction:")
    print(f"  SENSE: MSE={mse_sense:.5f}, PSNR={psnr_sense:.2f}dB")
    print(f"  SoS:   MSE={mse_sos:.5f}, PSNR={psnr_sos:.2f}dB")

# Visualize the reconstruction results
# Create a figure with rows for different coil counts and columns for different methods
plt.figure(figsize=(15, 12))

# Original image
plt.subplot(len(coil_configs)+1, 3, 1)
plt.imshow(image_data, cmap='gray')
plt.title("Original Image")
plt.axis('off')

plt.subplot(len(coil_configs)+1, 3, 2)
plt.imshow(np.log(np.abs(kspace_full) + 1), cmap='viridis')
plt.title("Full k-space")
plt.axis('off')

plt.subplot(len(coil_configs)+1, 3, 3)
plt.plot(kx_pixel - image_size/2, ky_pixel - image_size/2, 'b.', markersize=1)
plt.title(f"Spiral Trajectory\n({coverage:.1f}% coverage)")
plt.axis('equal')
plt.grid(True)

# Show reconstructions for different numbers of coils
for i, n_coils in enumerate(coil_configs):
    row = i + 1
    
    # SENSE reconstruction
    plt.subplot(len(coil_configs)+1, 3, row*3 + 1)
    plt.imshow(results[n_coils]['sense'], cmap='gray')
    plt.title(f"{n_coils}-ch SENSE\nPSNR={results[n_coils]['psnr_sense']:.1f}dB")
    plt.axis('off')
    
    # Sum-of-squares reconstruction
    plt.subplot(len(coil_configs)+1, 3, row*3 + 2)
    plt.imshow(results[n_coils]['sos'], cmap='gray')
    plt.title(f"{n_coils}-ch SoS\nPSNR={results[n_coils]['psnr_sos']:.1f}dB")
    plt.axis('off')
    
    # Error map
    plt.subplot(len(coil_configs)+1, 3, row*3 + 3)
    plt.imshow(np.abs(image_data - results[n_coils]['sense']), cmap='hot', vmin=0, vmax=0.2)
    plt.title(f"SENSE Error\nMSE={results[n_coils]['mse_sense']:.5f}")
    plt.axis('off')

plt.tight_layout()
plt.show()

# Plot PSNR vs number of coils
plt.figure(figsize=(10, 5))
plt.plot(coil_configs, [results[n]['psnr_sense'] for n in coil_configs], 'bo-', linewidth=2, label='SENSE')
plt.plot(coil_configs, [results[n]['psnr_sos'] for n in coil_configs], 'ro-', linewidth=2, label='Sum-of-squares')
plt.xlabel('Number of Coils')
plt.ylabel('PSNR (dB)')
plt.title('Reconstruction Quality vs. Number of Coils')
plt.grid(True)
plt.legend()
plt.tight_layout()
plt.show()

# Zoomed cardiac region comparison (using the best reconstruction)
# Find cardiac center
cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
for label in [1, 2, 5, 6]:  # Cardiac structures
    cardiac_mask |= (data['Labels'][:,:,z_slice] == label)

if np.any(cardiac_mask):
    y_indices, x_indices = np.where(cardiac_mask)
    zoom_y, zoom_x = int(np.mean(y_indices)), int(np.mean(x_indices))
else:
    zoom_y, zoom_x = image_size // 2, image_size // 2

# Define zoom parameters
zoom_size = 40
zoom_y_min = max(0, zoom_y-zoom_size)
zoom_y_max = min(image_size, zoom_y+zoom_size)
zoom_x_min = max(0, zoom_x-zoom_size)
zoom_x_max = min(image_size, zoom_x+zoom_size)

# Compare zoomed regions with best reconstruction (32-channel)
plt.figure(figsize=(15, 5))

# Original zoomed
plt.subplot(1, 3, 1)
plt.imshow(image_data[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title("Original (Zoomed)")
plt.axis('off')

# 32-ch SENSE zoomed
plt.subplot(1, 3, 2)
plt.imshow(results[32]['sense'][zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
plt.title("32-ch SENSE (Zoomed)")
plt.axis('off')

# Error map zoomed
plt.subplot(1, 3, 3)
plt.imshow(np.abs(image_data - results[32]['sense'])[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], 
          cmap='hot', vmin=0, vmax=0.2)
plt.title("Error (Zoomed)")
plt.colorbar()
plt.axis('off')

plt.tight_layout()
plt.show()

# Print summary table
print("\nMulti-Coil Spiral Reconstruction Summary:")
print(f"{'Coils':<6} {'Sampling':<10} {'SENSE PSNR':<15} {'SoS PSNR':<15} {'SENSE/SoS Gain':<15}")
print("-" * 65)

for n_coils in coil_configs:
    psnr_gain = results[n_coils]['psnr_sense'] - results[n_coils]['psnr_sos']
    print(f"{n_coils:<6} {coverage:.1f}%{'':<6} {results[n_coils]['psnr_sense']:.2f} dB{'':<5} "
          f"{results[n_coils]['psnr_sos']:.2f} dB{'':<5} {psnr_gain:.2f} dB")

import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter

# First, install SigPy if not already installed
# !pip install sigpy

import sigpy as sp
import sigpy.mri as mr

# Extract spiral trajectory from PyPulseq sequence
print("Using spiral trajectory from PyPulseq sequence")
print(f"Spiral parameters: {samples} samples, {readout_time*1000:.1f}ms readout")

# Get input T2*+ GRE image and convert to k-space
image_data = gre_t2star_plus_image
image_size = image_data.shape[0]
kspace_full = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(image_data)))
print(f"Input image shape: {image_data.shape}")
print(f"Full k-space shape: {kspace_full.shape}")

# Scale the PyPulseq trajectory to match our image pixel dimensions
k_scaling = (image_size/2) / kmax  # Scale factor from 1/m to pixels
kx_pixel = kx * k_scaling + image_size/2  # Shift to center
ky_pixel = ky * k_scaling + image_size/2
print(f"Trajectory points: {len(kx_pixel)}")

# Create a sampling mask for visualization
sampling_mask = np.zeros((image_size, image_size), dtype=bool)
kx_pixel_int = np.clip(np.round(kx_pixel).astype(int), 0, image_size-1)
ky_pixel_int = np.clip(np.round(ky_pixel).astype(int), 0, image_size-1)
for i in range(len(kx_pixel_int)):
    x, y = kx_pixel_int[i], ky_pixel_int[i]
    sampling_mask[y, x] = True

# Count coverage
coverage = np.sum(sampling_mask) / (image_size * image_size) * 100
print(f"k-space coverage: {coverage:.2f}% of pixels")

# Calculate density compensation function (DCF)
r_norm = np.sqrt((kx/kmax)**2 + (ky/kmax)**2)  # Normalized radius [0,1]
dcf = np.sqrt(r_norm + 0.1)  # Simple DCF with small offset to avoid zero at center

# Generate synthetic coil sensitivity maps
def generate_coil_sensitivity_maps(image_size, n_coils=8, coil_radius=1.2):
    """Generate synthetic coil sensitivity maps with rectangular FOV"""
    maps = np.zeros((n_coils, image_size, image_size), dtype=complex)
    
    # Create a grid of coordinates
    x = np.linspace(-1, 1, image_size)
    y = np.linspace(-1, 1, image_size)
    X, Y = np.meshgrid(x, y)
    
    # Define coil positions around a rectangular FOV
    aspect_ratio = 1.0  # For square FOV
    theta = np.linspace(0, 2*np.pi, n_coils, endpoint=False)
    coil_x = coil_radius * np.cos(theta)
    coil_y = coil_radius * aspect_ratio * np.sin(theta)
    
    # Generate sensitivity profile for each coil
    for i in range(n_coils):
        # Distance from each pixel to the coil
        distance = np.sqrt((X - coil_x[i])**2 + (Y - coil_y[i])**2)
        
        # Sensitivity falls off with distance (Gaussian model)
        sensitivity = np.exp(-distance**2 / (2 * 0.7**2))
        
        # Add some phase variation
        phase = np.angle(X + 1j*Y) * 0.3 - theta[i]
        complex_sensitivity = sensitivity * np.exp(1j * phase)
        
        # Apply a rectangular body mask to match FOV shape
        body_mask = (np.abs(X) <= 0.9) & (np.abs(Y) <= 0.9)
        maps[i, body_mask] = complex_sensitivity[body_mask]
        
        # Smooth the sensitivity map
        maps[i] = gaussian_filter(maps[i].real, sigma=0.8) + 1j * gaussian_filter(maps[i].imag, sigma=0.8)
    
    # Normalize the combined sensitivity maps
    sum_of_squares = np.sqrt(np.sum(np.abs(maps)**2, axis=0))
    valid_mask = sum_of_squares > 0
    
    for i in range(n_coils):
        maps[i, valid_mask] = maps[i, valid_mask] / sum_of_squares[valid_mask]
    
    return maps

# Generate coil sensitivity maps for different numbers of coils
coil_configs = [4, 8, 16, 32]
all_sensitivity_maps = {}
for n_coils in coil_configs:
    all_sensitivity_maps[n_coils] = generate_coil_sensitivity_maps(image_size, n_coils)
    print(f"Generated {n_coils}-channel coil sensitivity maps")

# Visualize the coil sensitivity maps for the 8-coil configuration
plt.figure(figsize=(15, 8))
n_coils_to_show = 8
sensitivity_maps = all_sensitivity_maps[n_coils_to_show]
for i in range(n_coils_to_show):
    plt.subplot(2, 4, i+1)
    plt.imshow(np.abs(sensitivity_maps[i]), cmap='viridis')
    plt.title(f"Coil {i+1} Sensitivity")
    plt.axis('off')
plt.tight_layout()
plt.show()

# Generate multi-coil k-space data
def generate_multicoil_kspace(image, sensitivity_maps):
    """Generate multi-coil k-space data"""
    n_coils = sensitivity_maps.shape[0]
    size_x, size_y = image.shape
    
    # Apply coil sensitivity maps to generate individual coil images
    coil_images = np.zeros((n_coils, size_x, size_y), dtype=complex)
    for i in range(n_coils):
        coil_images[i] = image * sensitivity_maps[i]
    
    # Transform to k-space
    coil_kspace = np.zeros_like(coil_images)
    for i in range(n_coils):
        coil_kspace[i] = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(coil_images[i])))
    
    return coil_kspace, coil_images

# Fixed SigPy reconstruction function
# Modified SigPy reconstruction function
def sigpy_spiral_recon(kspace_data, sensitivity_maps, trajectory, dcf=None, lambda_reg=0.01):
    """
    Non-Cartesian SENSE reconstruction for spiral MRI using SigPy's available methods
    
    Parameters:
    -----------
    kspace_data: array [n_coils, n_samples]
        Non-Cartesian k-space data along the trajectory
    sensitivity_maps: array [n_coils, nx, ny]
        Coil sensitivity maps
    trajectory: array [n_samples, 2]
        k-space coordinates normalized to [-0.5, 0.5]
    dcf: array [n_samples] or None
        Density compensation function
    lambda_reg: float
        Regularization parameter
    
    Returns:
    --------
    image: array [nx, ny]
        Reconstructed complex image
    """
    n_coils, nx, ny = sensitivity_maps.shape
    n_samples = trajectory.shape[0]
    
    # Ensure we're using SigPy's CPU device
    device = sp.cpu_device
    xp = device.xp  # Get the appropriate array module (numpy)
    
    # For debugging - print shapes and ranges
    print(f"Data shapes - kspace: {kspace_data.shape}, sens: {sensitivity_maps.shape}, coords: {trajectory.shape}")
    print(f"Coordinate range: [{np.min(trajectory):.3f}, {np.max(trajectory):.3f}]")
    if dcf is not None:
        print(f"DCF range: [{np.min(dcf):.3f}, {np.max(dcf):.3f}]")
    
    try:
        # Method 1: Use the NUFFT operators directly
        # Create the NUFFT operator
        print("Using SigPy's NUFFT operator for reconstruction")
        img_shape = (nx, ny)
        
        # Forward: image to k-space (non-uniform)
        forward_op = sp.linop.NUFFT(img_shape, trajectory)
        
        # Create sensitivity map operator
        sens_op = sp.linop.Multiply((n_coils,) + img_shape, sensitivity_maps)
        
        # Combined forward operator
        forward_op = sens_op * forward_op
        
        # Adjoint operator
        adjoint_op = forward_op.H  # Hermitian transpose
        
        # Apply density compensation
        if dcf is not None:
            print("Applying density compensation")
            ksp_weighted = kspace_data * dcf
        else:
            ksp_weighted = kspace_data
        
        # Initial estimate using adjoint (gridding)
        print("Getting initial estimate using adjoint")
        init_est = adjoint_op * ksp_weighted
        
        # Solve with conjugate gradient
        print(f"Running conjugate gradient solver with lambda={lambda_reg}")
        A = forward_op.H * forward_op
        b = adjoint_op * ksp_weighted
        
        # Create regularization operator (Tikhonov)
        I = sp.linop.Identity(img_shape)
        reg_op = lambda_reg * I
        
        # Use CG solver
        max_iter = 50
        print(f"Starting CG solver with {max_iter} iterations")
        
        img = sp.app.LinearLeastSquares(
            A=A, 
            y=b,
            x=init_est,
            proxg=sp.prox.L2Reg(lambda_reg),
            max_iter=max_iter
        ).run()
        
        return img

    except Exception as e:
        print(f"Method 1 failed: {str(e)}")
        
        # Method 2: Use simpler iterative SENSE approach
        try:
            print("Trying alternative reconstruction method")
            # Create k-space operator
            print("Creating NUFFT operator")
            F = sp.linop.NUFFT(img_shape, trajectory)
            
            # Create forward model
            print("Setting up forward model")
            A = sp.linop.Remix(F, sensitivity_maps)
            
            # Direct reconstruction using adjoint
            print("Initial solution using gridding")
            img = sp.app.LinearLeastSquares(A, ksp_weighted, lamda=lambda_reg, max_iter=max_iter).run()
            
            return img
            
        except Exception as e2:
            print(f"Method 2 failed: {str(e2)}")
            print("All SigPy methods failed, falling back to basic reconstruction")
            raise

# Function to prepare data for SigPy (proper format and normalization)
def prepare_for_sigpy(pixel_coords, dcf_values, multicoil_kspace):
    """
    Prepare data for SigPy reconstruction
    
    Parameters:
    -----------
    pixel_coords: array [n_samples, 2]
        k-space coordinates in pixel units
    dcf_values: array [n_samples]
        Density compensation values for each sample
    multicoil_kspace: array [n_coils, ny, nx]
        Full multi-coil k-space data
    
    Returns:
    --------
    coord_norm: array [n_samples, 2]
        Coordinates normalized to [-0.5, 0.5]
    kspace_data: array [n_coils, n_samples]
        k-space data sampled along trajectory
    dcf_norm: array [n_samples]
        Normalized density compensation function
    """
    n_coils = multicoil_kspace.shape[0]
    n_samples = len(pixel_coords)
    image_size = multicoil_kspace.shape[1]
    
    # Normalize coordinates to [-0.5, 0.5]
    coord_norm = np.zeros((n_samples, 2))
    coord_norm[:, 0] = pixel_coords[:, 0] / image_size - 0.5
    coord_norm[:, 1] = pixel_coords[:, 1] / image_size - 0.5
    
    # Ensure coordinates are within bounds
    coord_norm = np.clip(coord_norm, -0.5, 0.5 - 1e-6)
    
    # Extract k-space data along trajectory using bilinear interpolation
    kspace_data = np.zeros((n_coils, n_samples), dtype=complex)
    
    for c in range(n_coils):
        for i in range(n_samples):
            # Get pixel coordinates
            x = pixel_coords[i, 0]
            y = pixel_coords[i, 1]
            
            # Clip to valid range (for both floor and ceil operations)
            x = np.clip(x, 0, image_size - 1.001)
            y = np.clip(y, 0, image_size - 1.001)
            
            # Get interpolation indices
            x0, y0 = int(np.floor(x)), int(np.floor(y))
            x1, y1 = min(x0 + 1, image_size - 1), min(y0 + 1, image_size - 1)
            
            # Get interpolation weights
            wx = x - x0
            wy = y - y0
            
            # Bilinear interpolation
            kspace_data[c, i] = (
                (1 - wx) * (1 - wy) * multicoil_kspace[c, y0, x0] + 
                wx * (1 - wy) * multicoil_kspace[c, y0, x1] + 
                (1 - wx) * wy * multicoil_kspace[c, y1, x0] + 
                wx * wy * multicoil_kspace[c, y1, x1]
            )
    
    # Normalize DCF values
    dcf_norm = dcf_values / np.max(dcf_values)
    
    return coord_norm, kspace_data, dcf_norm

# Function for basic gridding reconstruction (fallback)
def gridding_sos_recon(kspace_data, coord, dcf, sensitivity_maps, image_size):
    """Simple gridding with sum-of-squares combination for fallback"""
    n_coils = sensitivity_maps.shape[0]
    
    # Create sparse k-space representation
    sparse_kspace = np.zeros((n_coils, image_size, image_size), dtype=complex)
    
    # Round coordinates for nearest-neighbor gridding
    coord_pixel = np.round(coord * image_size + image_size / 2).astype(int)
    coord_pixel = np.clip(coord_pixel, 0, image_size - 1)
    
    # Apply density compensation and place samples in sparse k-space
    for c in range(n_coils):
        for i in range(coord_pixel.shape[0]):
            x, y = coord_pixel[i, 0], coord_pixel[i, 1]
            sparse_kspace[c, y, x] += kspace_data[c, i] * dcf[i]
    
    # Apply Gaussian smoothing for simple gridding
    gridded_kspace = np.zeros_like(sparse_kspace)
    for c in range(n_coils):
        gridded_kspace[c] = gaussian_filter(sparse_kspace[c].real, sigma=0.7) + \
                            1j * gaussian_filter(sparse_kspace[c].imag, sigma=0.7)
    
    # Transform to image domain
    coil_images = np.zeros_like(gridded_kspace)
    for c in range(n_coils):
        coil_images[c] = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(gridded_kspace[c])))
    
    # Sum-of-squares combination
    recon_sos = np.sqrt(np.sum(np.abs(coil_images)**2, axis=0))
    
    # SENSE combination (simple version)
    recon_sense = np.zeros((image_size, image_size), dtype=complex)
    sense_weights = np.zeros((image_size, image_size), dtype=float)
    
    for c in range(n_coils):
        recon_sense += coil_images[c] * np.conj(sensitivity_maps[c])
        sense_weights += np.abs(sensitivity_maps[c])**2
    
    # Avoid division by zero
    sense_weights[sense_weights < 1e-6] = 1e-6
    recon_sense /= sense_weights
    
    return np.abs(recon_sense), recon_sos

# Compare reconstructions with different numbers of coils
results = {}

for n_coils in coil_configs:
    print(f"\nStarting {n_coils}-channel reconstruction...")
    sensitivity_maps = all_sensitivity_maps[n_coils]
    
    # Generate multi-coil k-space data
    multicoil_kspace, multicoil_images = generate_multicoil_kspace(image_data, sensitivity_maps)
    
    # Prepare for SigPy reconstruction
    # Combine kx_pixel and ky_pixel into trajectory array
    pixel_coords = np.stack([kx_pixel, ky_pixel], axis=1)
    
    # Process data for SigPy reconstruction
    coord_norm, kspace_data, dcf_norm = prepare_for_sigpy(pixel_coords, dcf, multicoil_kspace)
    
    try:
        # Scale lambda based on coil count
        lambda_reg = 0.01 * (n_coils / 8)
        
        print(f"Running SigPy reconstruction with {n_coils} channels, lambda={lambda_reg:.3f}")
        reconstructed_sense = sigpy_spiral_recon(
            kspace_data, 
            sensitivity_maps, 
            coord_norm, 
            dcf_norm, 
            lambda_reg
        )
        
        # Check if reconstruction was successful
        if reconstructed_sense is None or np.isnan(reconstructed_sense).any():
            raise ValueError("SigPy returned empty or NaN result")
            
        # Also create basic gridding reconstruction for comparison
        _, reconstructed_sos = gridding_sos_recon(
            kspace_data, 
            coord_norm, 
            dcf_norm, 
            sensitivity_maps, 
            image_size
        )
            
        # Calculate error metrics (SENSE)
        sense_magnitude = np.abs(reconstructed_sense)
        mse_sense = np.mean((image_data - sense_magnitude)**2)
        psnr_sense = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sense))
        
        # Calculate error metrics (SoS)
        mse_sos = np.mean((image_data - reconstructed_sos)**2)
        psnr_sos = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sos))
        
        # Store results
        results[n_coils] = {
            'sense': sense_magnitude,
            'sos': reconstructed_sos,
            'mse_sense': mse_sense,
            'psnr_sense': psnr_sense,
            'mse_sos': mse_sos,
            'psnr_sos': psnr_sos
        }
        
        print(f"{n_coils}-channel reconstruction successful:")
        print(f"  SigPy SENSE: MSE={mse_sense:.5f}, PSNR={psnr_sense:.2f}dB")
        print(f"  SoS:         MSE={mse_sos:.5f}, PSNR={psnr_sos:.2f}dB")
        
    except Exception as e:
        print(f"Error with {n_coils}-channel reconstruction: {str(e)}")
        print("Falling back to conventional gridding reconstruction...")
        
        # Fallback to basic gridding
        recon_sense, recon_sos = gridding_sos_recon(
            kspace_data, 
            coord_norm, 
            dcf_norm, 
            sensitivity_maps, 
            image_size
        )
        
        # Calculate metrics
        mse_sense = np.mean((image_data - recon_sense)**2)
        psnr_sense = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sense))
        
        mse_sos = np.mean((image_data - recon_sos)**2)
        psnr_sos = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sos))
        
        results[n_coils] = {
            'sense': recon_sense,
            'sos': recon_sos,
            'mse_sense': mse_sense,
            'psnr_sense': psnr_sense,
            'mse_sos': mse_sos,
            'psnr_sos': psnr_sos
        }
        
        print(f"{n_coils}-channel fallback reconstruction:")
        print(f"  Basic SENSE: MSE={mse_sense:.5f}, PSNR={psnr_sense:.2f}dB")
        print(f"  SoS:         MSE={mse_sos:.5f}, PSNR={psnr_sos:.2f}dB")

# Visualize the reconstruction results
plt.figure(figsize=(15, 12))

# Original image
plt.subplot(len(coil_configs)+1, 3, 1)
plt.imshow(image_data, cmap='gray')
plt.title("Original Image")
plt.axis('off')

plt.subplot(len(coil_configs)+1, 3, 2)
plt.imshow(np.log(np.abs(kspace_full) + 1), cmap='viridis')
plt.title("Full k-space")
plt.axis('off')

plt.subplot(len(coil_configs)+1, 3, 3)
plt.plot(kx_pixel - image_size/2, ky_pixel - image_size/2, 'b.', markersize=1)
plt.title(f"Spiral Trajectory\n({coverage:.1f}% coverage)")
plt.axis('equal')
plt.grid(True)

# Show reconstructions for different numbers of coils
for i, n_coils in enumerate(coil_configs):
    if n_coils not in results:
        continue
        
    row = i + 1
    
    # SENSE reconstruction
    plt.subplot(len(coil_configs)+1, 3, row*3 + 1)
    plt.imshow(results[n_coils]['sense'], cmap='gray')
    plt.title(f"{n_coils}-ch SENSE\nPSNR={results[n_coils]['psnr_sense']:.1f}dB")
    plt.axis('off')
    
    # Sum-of-squares reconstruction
    plt.subplot(len(coil_configs)+1, 3, row*3 + 2)
    plt.imshow(results[n_coils]['sos'], cmap='gray')
    plt.title(f"{n_coils}-ch SoS\nPSNR={results[n_coils]['psnr_sos']:.1f}dB")
    plt.axis('off')
    
    # Error map
    plt.subplot(len(coil_configs)+1, 3, row*3 + 3)
    plt.imshow(np.abs(image_data - results[n_coils]['sense']), cmap='hot', vmin=0, vmax=0.2)
    plt.title(f"SENSE Error\nMSE={results[n_coils]['mse_sense']:.5f}")
    plt.axis('off')

plt.tight_layout()
plt.show()

# Check if we have any successful reconstructions
if not results:
    print("No successful reconstructions to plot")
else:
    # Create a list of coil configurations that were successful
    successful_configs = sorted(list(results.keys()))
    
    # Plot PSNR vs number of coils
    plt.figure(figsize=(10, 5))
    plt.plot(successful_configs, [results[n]['psnr_sense'] for n in successful_configs], 'bo-', 
             linewidth=2, label='SENSE')
    plt.plot(successful_configs, [results[n]['psnr_sos'] for n in successful_configs], 'ro-', 
             linewidth=2, label='Sum-of-squares')
    plt.xlabel('Number of Coils')
    plt.ylabel('PSNR (dB)')
    plt.title('Reconstruction Quality vs. Number of Coils')
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.show()
    
    # For the best configuration, show zoom of cardiac region
    n_best = successful_configs[-1]  # Use the highest coil count
    
    # Find cardiac center
    cardiac_mask = np.zeros_like(data['Labels'][:,:,z_slice], dtype=bool)
    for label in [1, 2, 5, 6]:  # Cardiac structures
        cardiac_mask |= (data['Labels'][:,:,z_slice] == label)

    if np.any(cardiac_mask):
        y_indices, x_indices = np.where(cardiac_mask)
        zoom_y, zoom_x = int(np.mean(y_indices)), int(np.mean(x_indices))
    else:
        zoom_y, zoom_x = image_size // 2, image_size // 2

    # Define zoom parameters
    zoom_size = 40
    zoom_y_min = max(0, zoom_y-zoom_size)
    zoom_y_max = min(image_size, zoom_y+zoom_size)
    zoom_x_min = max(0, zoom_x-zoom_size)
    zoom_x_max = min(image_size, zoom_x+zoom_size)
    
    # Show zoomed comparison
    plt.figure(figsize=(15, 5))
    plt.subplot(1, 3, 1)
    plt.imshow(image_data[zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
    plt.title("Original (Zoomed)")
    plt.axis('off')
    
    plt.subplot(1, 3, 2)
    plt.imshow(results[n_best]['sense'][zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
    plt.title(f"{n_best}-ch SENSE (Zoomed)")
    plt.axis('off')
    
    plt.subplot(1, 3, 3)
    plt.imshow(results[n_best]['sos'][zoom_y_min:zoom_y_max, zoom_x_min:zoom_x_max], cmap='gray')
    plt.title(f"{n_best}-ch SoS (Zoomed)")
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()

import numpy as np
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter

# First, install SigPy if not already installed
# !pip install sigpy

import sigpy as sp
import sigpy.mri as mr

# Extract spiral trajectory from PyPulseq sequence (from previous cells)
print("Using spiral trajectory from PyPulseq sequence")
print(f"Spiral parameters: {samples} samples, {readout_time*1000:.1f}ms readout")

# Convert the PyPulseq k-space trajectory to match our image dimensions
# Get input T2*+ GRE image and convert to k-space
image_data = gre_t2star_plus_image
image_size = image_data.shape[0]
# kspace_full = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(image_data)))
# bssfp_full
print(f"Input image shape: {image_data.shape}")
print(f"Full k-space shape: {kspace_full.shape}")

# Scale the PyPulseq trajectory to match our image pixel dimensions
k_scaling = (image_size/2) / kmax  # Scale factor from 1/m to pixels
kx_pixel = kx * k_scaling + image_size/2  # Shift to center
ky_pixel = ky * k_scaling + image_size/2
print(f"Trajectory points: {len(kx_pixel)}")

# Create a sampling mask and sampled k-space data
sampling_mask = np.zeros((image_size, image_size), dtype=bool)
for i in range(len(kx_pixel_int)):
    x, y = kx_pixel_int[i], ky_pixel_int[i]
    sampling_mask[y, x] = True

# Count coverage
coverage = np.sum(sampling_mask) / (image_size * image_size) * 100
print(f"k-space coverage: {coverage:.2f}% of pixels")

# Calculate density compensation function (DCF)
r_norm = np.sqrt((kx/kmax)**2 + (ky/kmax)**2)  # Normalized radius [0,1]
dcf = np.sqrt(r_norm + 0.1)  # Simple DCF with small offset to avoid zero at center

# ===== Keep existing coil sensitivity map generation =====
def generate_coil_sensitivity_maps(image_size, n_coils=8, coil_radius=1.2):
    """Generate synthetic coil sensitivity maps with rectangular FOV"""
    maps = np.zeros((n_coils, image_size, image_size), dtype=complex)
    
    # Create a grid of coordinates
    x = np.linspace(-1, 1, image_size)
    y = np.linspace(-1, 1, image_size)
    X, Y = np.meshgrid(x, y)
    
    # Define coil positions around a rectangular FOV
    aspect_ratio = 1.0  # Adjust if your FOV isn't square
    theta = np.linspace(0, 2*np.pi, n_coils, endpoint=False)
    coil_x = coil_radius * np.cos(theta)
    coil_y = coil_radius * aspect_ratio * np.sin(theta)
    
    # Generate sensitivity profile for each coil
    for i in range(n_coils):
        # Distance from each pixel to the coil
        distance = np.sqrt((X - coil_x[i])**2 + (Y - coil_y[i])**2)
        
        # Sensitivity falls off with distance (Gaussian model)
        sensitivity = np.exp(-distance**2 / (2 * 0.7**2))
        
        # Add some phase variation
        phase = np.angle(X + 1j*Y) * 0.3 - theta[i]
        complex_sensitivity = sensitivity * np.exp(1j * phase)
        
        # Apply a rectangular body mask to match FOV shape
        body_mask = (np.abs(X) <= 0.9) & (np.abs(Y) <= 0.9)
        maps[i, body_mask] = complex_sensitivity[body_mask]
        
        # Smooth the sensitivity map
        maps[i] = gaussian_filter(maps[i].real, sigma=0.8) + 1j * gaussian_filter(maps[i].imag, sigma=0.8)
    
    # Normalize the combined sensitivity maps
    sum_of_squares = np.sqrt(np.sum(np.abs(maps)**2, axis=0))
    valid_mask = sum_of_squares > 0
    
    for i in range(n_coils):
        maps[i, valid_mask] = maps[i, valid_mask] / sum_of_squares[valid_mask]
    
    return maps

# Generate coil sensitivity maps for different numbers of coils
coil_configs = [4, 8, 16, 32]
all_sensitivity_maps = {}
for n_coils in coil_configs:
    all_sensitivity_maps[n_coils] = generate_coil_sensitivity_maps(image_size, n_coils)
    print(f"Generated {n_coils}-channel coil sensitivity maps")

# Visualize the coil sensitivity maps for the 8-coil configuration
plt.figure(figsize=(15, 8))
n_coils_to_show = 8
sensitivity_maps = all_sensitivity_maps[n_coils_to_show]
for i in range(n_coils_to_show):
    plt.subplot(2, 4, i+1)
    plt.imshow(np.abs(sensitivity_maps[i]), cmap='viridis')
    plt.title(f"Coil {i+1} Sensitivity")
    plt.axis('off')
plt.tight_layout()
plt.show()

# ===== Generate undersampled multi-coil k-space data =====
def generate_multicoil_kspace(image, sensitivity_maps, sampling_mask=None):
    """Generate multi-coil k-space data with optional undersampling"""
    n_coils = sensitivity_maps.shape[0]
    size_x, size_y = image.shape
    
    # Apply coil sensitivity maps to generate individual coil images
    coil_images = np.zeros((n_coils, size_x, size_y), dtype=complex)
    for i in range(n_coils):
        coil_images[i] = image * sensitivity_maps[i]
    
    # Transform to k-space
    coil_kspace = np.zeros_like(coil_images)
    for i in range(n_coils):
        coil_kspace[i] = np.fft.fftshift(np.fft.fft2(np.fft.ifftshift(coil_images[i])))
    
    # Apply sampling mask if provided
    if sampling_mask is not None:
        for i in range(n_coils):
            coil_kspace[i] *= sampling_mask
    
    return coil_kspace, coil_images

# ===== NEW: SigPy-based SENSE reconstruction =====
# Fix the SigPy-based SENSE reconstruction function
def sigpy_sense_reconstruction(trajectory, kspace_data, sensitivity_maps, dcf=None, lambda_reg=0.01):
    """
    Fixed SENSE reconstruction for spiral MRI using SigPy
    """
    n_coils = sensitivity_maps.shape[0]
    nx, ny = sensitivity_maps.shape[1:3]
    n_samples = trajectory.shape[0]
    
    print(f"Starting reconstruction: {n_coils} coils, {n_samples} spiral samples, image size {nx}x{ny}")
    
    # SigPy requires coordinates between -0.5 and 0.5
    # The issue is that we're currently using pixel coordinates (0 to image_size-1)
    # Need to convert to proper normalized coordinates
    coord = np.zeros((n_samples, 2))
    coord[:, 0] = trajectory[:, 0] / nx - 0.5  # Normalize x-coordinates
    coord[:, 1] = trajectory[:, 1] / nx - 0.5  # Normalize y-coordinates
    
    print(f"Coordinate range: [{np.min(coord):.3f} to {np.max(coord):.3f}]")
    
    # Make sure coordinates are within bounds
    coord = np.clip(coord, -0.5, 0.5)
    
    # Reshape DCF to match kspace_data if provided
    if dcf is not None:
        dcf_reshape = dcf.reshape(-1)
    else:
        dcf_reshape = None
    
    try:
        # Use lower-level components for better control
        # Create NUFFT operator
        F = sp.linop.NUFFT([nx, ny], coord)
        
        # Create sensitivity encoding operator
        S = sp.linop.Multiply([n_coils, nx, ny], sensitivity_maps)
        
        # Forward operator: from image to k-space samples
        A = S * F
        
        # Apply density compensation
        y = kspace_data
        if dcf_reshape is not None:
            for c in range(n_coils):
                y[c] = y[c] * dcf_reshape
        
        # Reconstruct with conjugate gradient
        print(f"Solving reconstruction with lambda={lambda_reg}")
        img = sp.app.LinearLeastSquares(A, y, lamda=lambda_reg).run()
        
        return img
    except Exception as e:
        print(f"Low-level SigPy reconstruction failed: {e}")
        raise

# Format the trajectory data for SigPy
def prepare_for_sigpy(kx_pixel, ky_pixel, dcf, kspace):
    """Format trajectory and k-space data for SigPy"""
    # Reshape trajectory for SigPy
    trajectory = np.stack([kx_pixel, ky_pixel], axis=1)
    
    # Extract sparse k-space data as samples
    n_coils = kspace.shape[0]
    n_samples = len(kx_pixel)
    kspace_data = np.zeros((n_coils, n_samples), dtype=complex)
    
    # Extract samples from k-space at trajectory locations
    for c in range(n_coils):
        for i in range(n_samples):
            x = min(max(int(kx_pixel[i]), 0), kspace.shape[2]-1)
            y = min(max(int(ky_pixel[i]), 0), kspace.shape[1]-1)
            kspace_data[c, i] = kspace[c, y, x]
            
    return trajectory, kspace_data, dcf

# Compare reconstructions with different numbers of coils
results = {}

for n_coils in coil_configs:
    sensitivity_maps = all_sensitivity_maps[n_coils]
    
    # Generate multi-coil k-space data
    multicoil_kspace, multicoil_images = generate_multicoil_kspace(image_data, sensitivity_maps)
    
    # Important: Use floating point coordinates for trajectory
    # Integer pixel values cause problems with SigPy's interpolation
    trajectory_data = np.zeros((len(kx_pixel), 2))
    trajectory_data[:, 0] = kx_pixel  # Use the original floating point values
    trajectory_data[:, 1] = ky_pixel
    
    # Extract k-space data along trajectory points
    sparse_kspace_data = np.zeros((n_coils, len(kx_pixel)), dtype=complex)
    
    # For each coil, extract k-space values along trajectory using nearest neighbor
    for c in range(n_coils):
        # Use bilinear interpolation for better accuracy
        for i in range(len(kx_pixel)):
            x_floor = int(np.floor(kx_pixel[i]))
            y_floor = int(np.floor(ky_pixel[i]))
            x_ceil = min(x_floor + 1, image_size - 1)
            y_ceil = min(y_floor + 1, image_size - 1)
            
            # Ensure indices are within bounds
            x_floor = max(0, min(x_floor, image_size - 1))
            y_floor = max(0, min(y_floor, image_size - 1))
            
            # Get interpolation weights
            wx = kx_pixel[i] - x_floor
            wy = ky_pixel[i] - y_floor
            
            # Bilinear interpolation
            val = (1-wx)*(1-wy)*multicoil_kspace[c, y_floor, x_floor] + \
                  wx*(1-wy)*multicoil_kspace[c, y_floor, x_ceil] + \
                  (1-wx)*wy*multicoil_kspace[c, y_ceil, x_floor] + \
                  wx*wy*multicoil_kspace[c, y_ceil, x_ceil]
            
            sparse_kspace_data[c, i] = val
    
    try:
        # Try SigPy-based SENSE reconstruction
        # Adjust lambda based on number of coils
        lambda_reg = 0.01 * (n_coils / 8)
        reconstructed_sense = sigpy_sense_reconstruction(
            trajectory_data, sparse_kspace_data, sensitivity_maps, dcf, lambda_reg
        )
        
        # For comparison, create sparse Cartesian k-space with samples
        sparse_multicoil_kspace = np.zeros_like(multicoil_kspace)
        for c in range(n_coils):
            for i in range(len(kx_pixel_int)):
                x, y = kx_pixel_int[i], ky_pixel_int[i]
                sparse_multicoil_kspace[c, y, x] = multicoil_kspace[c, y, x] * dcf[i]
        
        # Simple sum-of-squares reconstruction for comparison
        gridded_kspace = np.zeros_like(sparse_multicoil_kspace)
        for c in range(n_coils):
            gridded_kspace[c] = gaussian_filter(sparse_multicoil_kspace[c].real, sigma=0.5) + \
                               1j * gaussian_filter(sparse_multicoil_kspace[c].imag, sigma=0.5)
        
        sos_images = np.zeros_like(sparse_multicoil_kspace, dtype=complex)
        for c in range(n_coils):
            sos_images[c] = np.fft.ifftshift(np.fft.ifft2(np.fft.fftshift(gridded_kspace[c])))
        
        reconstructed_sos = np.sqrt(np.sum(np.abs(sos_images)**2, axis=0))
        
        # Calculate error metrics
        mse_sense = np.mean(np.abs(image_data - np.abs(reconstructed_sense))**2)
        psnr_sense = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sense))
        
        mse_sos = np.mean(np.abs(image_data - reconstructed_sos)**2)
        psnr_sos = 20 * np.log10(np.max(image_data) / np.sqrt(mse_sos))
        
        results[n_coils] = {
            'sense': np.abs(reconstructed_sense),
            'sos': reconstructed_sos,
            'mse_sense': mse_sense,
            'psnr_sense': psnr_sense,
            'mse_sos': mse_sos,
            'psnr_sos': psnr_sos
        }
        
        print(f"{n_coils}-channel reconstruction:")
        print(f"  SigPy SENSE: MSE={mse_sense:.5f}, PSNR={psnr_sense:.2f}dB")
        print(f"  SoS:         MSE={mse_sos:.5f}, PSNR={psnr_sos:.2f}dB")
        
    except Exception as e:
        print(f"Error with {n_coils}-channel reconstruction: {e}")
        # Fallback to previous implementation if SigPy fails
        print("Falling back to conventional reconstruction...")

# ===== Keep the rest of visualization code unchanged =====

# Visualize the reconstruction results
# Create a figure with rows for different coil counts and columns for different methods
plt.figure(figsize=(15, 12))

# Original image
plt.subplot(len(coil_configs)+1, 3, 1)
plt.imshow(image_data, cmap='gray')
plt.title("Original Image")
plt.axis('off')

plt.subplot(len(coil_configs)+1, 3, 2)
plt.imshow(np.log(np.abs(kspace_full) + 1), cmap='viridis')
plt.title("Full k-space")
plt.axis('off')

plt.subplot(len(coil_configs)+1, 3, 3)
plt.plot(kx_pixel - image_size/2, ky_pixel - image_size/2, 'b.', markersize=1)
plt.title(f"Spiral Trajectory\n({coverage:.1f}% coverage)")
plt.axis('equal')
plt.grid(True)

# Show reconstructions for different numbers of coils
for i, n_coils in enumerate(coil_configs):
    if n_coils not in results:
        continue
        
    row = i + 1
    
    # SENSE reconstruction
    plt.subplot(len(coil_configs)+1, 3, row*3 + 1)
    plt.imshow(results[n_coils]['sense'], cmap='gray')
    plt.title(f"{n_coils}-ch SigPy SENSE\nPSNR={results[n_coils]['psnr_sense']:.1f}dB")
    plt.axis('off')
    
    # Sum-of-squares reconstruction
    plt.subplot(len(coil_configs)+1, 3, row*3 + 2)
    plt.imshow(results[n_coils]['sos'], cmap='gray')
    plt.title(f"{n_coils}-ch SoS\nPSNR={results[n_coils]['psnr_sos']:.1f}dB")
    plt.axis('off')
    
    # Error map
    plt.subplot(len(coil_configs)+1, 3, row*3 + 3)
    plt.imshow(np.abs(image_data - results[n_coils]['sense']), cmap='hot', vmin=0, vmax=0.2)
    plt.title(f"SENSE Error\nMSE={results[n_coils]['mse_sense']:.5f}")
    plt.axis('off')

plt.tight_layout()
plt.show()

# Plot PSNR vs number of coils
plt.figure(figsize=(10, 5))
plt.plot(coil_configs, [results[n]['psnr_sense'] for n in coil_configs], 'bo-', 
         linewidth=2, label='SigPy SENSE')
plt.plot(coil_configs, [results[n]['psnr_sos'] for n in coil_configs], 'ro-', 
         linewidth=2, label='Sum-of-squares')
plt.xlabel('Number of Coils')
plt.ylabel('PSNR (dB)')
plt.title('Reconstruction Quality vs. Number of Coils')
plt.grid(True)
plt.legend()
plt.tight_layout()
plt.show()

import pypulseq as pp
import numpy as np
import matplotlib.pyplot as plt

# Define system limits
system = pp.Opts(max_grad=200, grad_unit='mT/m', max_slew=200, slew_unit='mT/m/ms')

# Create sequence object
seq = pp.Sequence(system=system)

# Specific parameters
fov_xy = 150e-3       # FOV in meters
resolution = 2e-3     # 2mm resolution
n_slices = 150        # Number of slices
slice_thickness = 2e-3  # 2mm slice thickness
slice_gap = 0         # No gap between slices

# Calculate number of pixels based on FOV and resolution
nx = int(np.round(fov_xy / resolution))  # Number of samples in x direction
ny = int(np.round(fov_xy / resolution))  # Number of samples in y direction

print(f"Matrix size: {nx}x{ny}")

# Spiral parameters
dt = 4e-6             # Spiral sampling time in seconds
kmax = nx/(2*fov_xy)  # Maximum k-space value (1/m)
readout_time = 50e-3   # Spiral readout duration in seconds
samples = int(readout_time / dt)  # Number of samples

# Create spiral trajectory (Archimedean spiral)
t = np.linspace(0, readout_time, samples)
theta = 2 * np.pi * 236 * t / readout_time  # 236 spiral turns
r = kmax * t / readout_time  # Linear increase in radius

# Convert to Cartesian coordinates
kx = r * np.cos(theta)
ky = r * np.sin(theta)

# Calculate gradients (G = dk/dt * gamma)
gamma = 42.576e6  # Hz/T, gyromagnetic ratio for protons
gx = np.diff(kx) / dt * 2 * np.pi / gamma
gy = np.diff(ky) / dt * 2 * np.pi / gamma

# Add one point to have same array size
gx = np.append(gx, gx[-1])
gy = np.append(gy, gy[-1])

# Scale down gradients if they exceed limits
max_grad_Hz_per_m = system.max_grad * 1e-3 * gamma  # Convert from mT/m to Hz/m
g_max = max(np.max(np.abs(gx)), np.max(np.abs(gy)))
if g_max > max_grad_Hz_per_m:
    scale_factor = max_grad_Hz_per_m / g_max
    gx *= scale_factor
    gy *= scale_factor
    print(f"Gradients scaled down by factor {scale_factor:.3f} to meet system limits")

# For demonstration, just create one slice
# In a full implementation, you would loop through all slices

# Create RF excitation pulse
rf = pp.make_sinc_pulse(flip_angle=15 * np.pi / 180,  # Use smaller flip angle for faster repetitions
                       duration=2e-3,
                       system=system,
                       time_bw_product=4,
                       slice_thickness=slice_thickness)

# Create slice selection gradient
gz = pp.make_trapezoid(channel='z', system=system, 
                      amplitude=1, # This would be calculated based on slice position
                      duration=1e-3)

# Create arbitrary gradient objects
grad_x = pp.make_arbitrary_grad(channel='x', waveform=gx, system=system)
grad_y = pp.make_arbitrary_grad(channel='y', waveform=gy, system=system)

# Create ADC event with same number of samples as the spiral trajectory
adc = pp.make_adc(num_samples=samples,
                 duration=readout_time,
                 system=system)

# Add blocks to sequence
seq.add_block(rf, gz)  # Slice selection
delay = pp.make_delay(1e-3)
seq.add_block(delay)
seq.add_block(grad_x, grad_y, adc)  # Readout with spiral gradients

# Calculate sequence duration
duration = seq.duration()
print(f"Sequence duration: {duration} s")

# Visualize sequence
seq.plot()
plt.savefig('spiral_sequence_plot.png')
plt.show()

# Plot the k-space trajectory
plt.figure()
plt.plot(kx, ky)
plt.title('Spiral K-space trajectory')
plt.xlabel('kx (1/m)')
plt.ylabel('ky (1/m)')
plt.axis('equal')
plt.grid(True)
plt.savefig('spiral_kspace.png')
plt.show()

# Write sequence to file
# seq.write('spiral_sequence.seq')

import pypulseq as pp
import numpy as np
import matplotlib.pyplot as plt

# Define system limits
system = pp.Opts(max_grad=200, grad_unit='mT/m', max_slew=200, slew_unit='mT/m/ms')

# Create sequence object
seq = pp.Sequence(system=system)

# Specific parameters
fov_xy = 150e-3       # FOV in meters
resolution = 2e-3     # 2mm resolution
n_slices = 5          # Start with fewer slices for testing
slice_thickness = 2e-3  # 2mm slice thickness
slice_gap = 0         # No gap between slices

# Calculate number of pixels based on FOV and resolution
nx = int(np.round(fov_xy / resolution))  # Number of samples in x direction
ny = int(np.round(fov_xy / resolution))  # Number of samples in y direction

print(f"Matrix size: {nx}x{ny}")

# Spiral parameters
dt = 4e-6             # Spiral sampling time in seconds
kmax = nx/(2*fov_xy)  # Maximum k-space value (1/m)
readout_time = 50e-3   # Spiral readout duration in seconds
samples = int(readout_time / dt)  # Number of samples

# Create spiral trajectory (Archimedean spiral)
t = np.linspace(0, readout_time, samples)
theta = 2 * np.pi * 236 * t / readout_time  # 12 spiral turns
r = kmax * t / readout_time  # Linear increase in radius

# Convert to Cartesian coordinates
kx = r * np.cos(theta)
ky = r * np.sin(theta)

# Calculate gradients (G = dk/dt * gamma)
gamma = 42.576e6  # Hz/T, gyromagnetic ratio for protons
gx = np.diff(kx) / dt * 2 * np.pi / gamma
gy = np.diff(ky) / dt * 2 * np.pi / gamma

# Add one point to have same array size
gx = np.append(gx, gx[-1])
gy = np.append(gy, gy[-1])

# Scale down gradients if they exceed limits
max_grad_Hz_per_m = system.max_grad * 1e-3 * gamma  # Convert from mT/m to Hz/m
g_max = max(np.max(np.abs(gx)), np.max(np.abs(gy)))
if g_max > max_grad_Hz_per_m:
    scale_factor = max_grad_Hz_per_m / g_max
    gx *= scale_factor
    gy *= scale_factor
    print(f"Gradients scaled down by factor {scale_factor:.3f} to meet system limits")

# Create arbitrary gradient objects
grad_x = pp.make_arbitrary_grad(channel='x', waveform=gx, system=system)
grad_y = pp.make_arbitrary_grad(channel='y', waveform=gy, system=system)

# Create ADC event with same number of samples as the spiral trajectory
adc = pp.make_adc(num_samples=samples,
                 duration=readout_time,
                 system=system)

delay = pp.make_delay(1e-3)

# Calculate and display ADC rate and bandwidth
adc_rate = samples / readout_time
adc_bandwidth = 1 / dt
print(f"ADC samples: {samples}")
print(f"ADC sampling rate: {adc_rate/1000:.2f} kHz")
print(f"ADC bandwidth: {adc_bandwidth/1000:.2f} kHz")

# Calculate and display total scan time
TR = 20e-3  # Repetition time
n_interleaves = 1     # Number of spiral interleaves (arms)
total_scan_time_single_interleave = n_slices * TR
total_scan_time_full = n_slices * n_interleaves * TR

print(f"Total scan time for {n_slices} slices with {n_interleaves} interleave(s): {total_scan_time_single_interleave*1000:.1f} ms")
print(f"Estimated full scan time for 150 slices with {n_interleaves} interleave(s): {150*n_interleaves*TR*1000:.1f} ms")

# For multiple interleaves, the rotation angle for each interleave
if n_interleaves > 1:
    rotation_angle = 2 * np.pi / n_interleaves
    print(f"Rotation angle between interleaves: {rotation_angle*180/np.pi:.1f} degrees")

# Full 3D acquisition with all slices - FIXED VERSION
for slice_idx in range(n_slices):
    # Calculate slice position (from -FOV/2 to FOV/2)
    slice_pos = (slice_idx - n_slices/2) * slice_thickness
    
    # Create slice selective RF pulse for this position
    # NOTE: Instead of using return_gz=True, we'll create the RF and gradients separately
    rf = pp.make_sinc_pulse(flip_angle=15 * np.pi / 180,
                          duration=2e-3,
                          system=system,
                          slice_thickness=slice_thickness,
                          apodization=0.5,
                          time_bw_product=4)
    
    # Create a slice selection gradient based on the slice position
    # The amplitude needs to be proportional to the slice offset
    amplitude = 1.0  # Base amplitude
    if slice_idx > 0:  # Adjust for off-center slices
        amplitude += slice_pos / (n_slices * slice_thickness) 
    
    # Create the slice selection gradient
    gz = pp.make_trapezoid(channel='z', 
                         system=system,
                         amplitude=amplitude,
                         duration=2e-3)
    
    # Add sequence blocks
    seq.add_block(rf, gz)
    seq.add_block(delay)
    seq.add_block(grad_x, grad_y, adc)
    
    # Add TR delay if needed
    remaining_time = TR - (pp.calc_duration(rf) + pp.calc_duration(delay) + readout_time)
    if remaining_time > 0:
        delay_TR = pp.make_delay(remaining_time)
        seq.add_block(delay_TR)

# Calculate sequence duration
duration_result = seq.duration()
# Check if duration is a tuple and extract the first value if it is
if isinstance(duration_result, tuple):
    duration = duration_result[0]  # Extract the first element of the tuple
else:
    duration = duration_result  # Use as is if it's not a tuple

print(f"Actual sequence duration: {duration*1000:.1f} ms")

# Similarly update the file writing part
# with open('spiral_sequence_info.txt', 'w') as f:
#     f.write(f"Spiral Sequence Parameters\n")
#     f.write(f"-------------------------\n")
#     # ...other lines...
#     f.write(f"Actual sequence duration: {duration*1000:.1f} ms\n")
#     f.write(f"Estimated full scan time (150 slices): {150*n_interleaves*TR*1000:.1f} ms\n")

# Write information to text file
with open('spiral_sequence_info.txt', 'w') as f:
    f.write(f"Spiral Sequence Parameters\n")
    f.write(f"-------------------------\n")
    f.write(f"FOV: {fov_xy*1000:.1f} mm x {fov_xy*1000:.1f} mm\n")
    f.write(f"Resolution: {resolution*1000:.1f} mm x {resolution*1000:.1f} mm\n")
    f.write(f"Matrix size: {nx}x{ny}\n")
    f.write(f"Slice thickness: {slice_thickness*1000:.1f} mm\n")
    f.write(f"Number of slices: {n_slices} (sample), 150 (full)\n")
    f.write(f"Number of interleaves: {n_interleaves}\n")
    f.write(f"Repetition time (TR): {TR*1000:.1f} ms\n")
    f.write(f"Readout time: {readout_time*1000:.1f} ms\n")
    f.write(f"ADC samples per spiral: {samples}\n")
    f.write(f"ADC sampling rate: {adc_rate/1000:.2f} kHz\n")
    f.write(f"ADC bandwidth: {adc_bandwidth/1000:.2f} kHz\n")
    f.write(f"Actual sequence duration: {duration*1000:.1f} ms\n")
    f.write(f"Estimated full scan time (150 slices): {150*n_interleaves*TR*1000:.1f} ms\n")

# Visualize sequence (only the first few blocks)
seq.plot(time_range=[0, 5*TR])  # Display first 5 TRs
plt.savefig('spiral_sequence_plot.png')
plt.show()

# Plot the k-space trajectory
plt.figure()
plt.plot(kx, ky)
plt.title('Spiral K-space trajectory')
plt.xlabel('kx (1/m)')
plt.ylabel('ky (1/m)')
plt.axis('equal')
plt.grid(True)
plt.savefig('spiral_kspace.png')
plt.show()

# Write sequence to file
# seq.write('spiral_sequence_3d.seq')

import numpy as np
import matplotlib.pyplot as plt

# -----------------------------------------------------------------------------
# Basic Parameter Definition
# -----------------------------------------------------------------------------
fov_xy = 15e-2         # Field of view (m), 24cm
N = 64                 # Matrix size
resolution = 2e-3  # Resolution (m)
max_k = 1/(2*resolution)  # Maximum k-space radius (cycles/m)
num_samples = 4000     # Number of sampling points

# Spiral parameter: k = a·θ
a = 1 / (2 * np.pi * fov_xy)  # Spiral spacing parameter (cycles/radian)

# Calculate maximum angle needed to reach max_k
max_theta = max_k / a

# -----------------------------------------------------------------------------
# Generate Spiral Trajectory
# -----------------------------------------------------------------------------
# Method 1: Uniform angle increment, uniform angular (non-uniform arc length) sampling
theta = np.linspace(0, max_theta, num_samples)
k = a * theta
kx_1 = k * np.cos(theta)
ky_1 = k * np.sin(theta)

# Method 2: Uniform k-space radius increment, uniform sampling from center to edge
k_2 = np.linspace(0, max_k, num_samples)
theta_2 = k_2 / a
kx_2 = k_2 * np.cos(theta_2)
ky_2 = k_2 * np.sin(theta_2)

# Method 3: Uniform arc length increment, equidistant sampling along trajectory
# Spiral arc length approximation: s = (a/2)*(θ*√(1+θ²) + ln(θ+√(1+θ²)))
def spiral_arclength(theta, a):
    return (a/2)*(theta*np.sqrt(1+theta**2) + np.log(theta+np.sqrt(1+theta**2)))

total_length = spiral_arclength(max_theta, a)
target_lengths = np.linspace(0, total_length, num_samples)

# Calculate corresponding angles (approximate calculation)
theta_3 = np.zeros(num_samples)
theta_3[0] = 0.001  # Avoid division by zero
# Binary search approach for better efficiency with high sample counts
for i in range(1, num_samples):
    length_target = target_lengths[i]
    # Initial bounds - previous angle and a reasonable upper limit
    theta_low = theta_3[i-1]
    theta_high = max(theta_low * 1.5, theta_low + 1.0)
    
    # Expand upper bound if needed
    while spiral_arclength(theta_high, a) < length_target:
        theta_high *= 2
    
    # Binary search to find the angle
    while (theta_high - theta_low) > 1e-6:  # Precision threshold
        theta_mid = (theta_low + theta_high) / 2
        length_mid = spiral_arclength(theta_mid, a)
        
        if length_mid < length_target:
            theta_low = theta_mid
        else:
            theta_high = theta_mid
    
    theta_3[i] = (theta_low + theta_high) / 2

# Calculate corresponding k-space coordinates
k_3 = a * theta_3
kx_3 = k_3 * np.cos(theta_3)
ky_3 = k_3 * np.sin(theta_3)

# -----------------------------------------------------------------------------
# Visualization
# -----------------------------------------------------------------------------
plt.figure(figsize=(16, 12))

# 1. Comparison of three sampling methods
plt.subplot(2, 2, 1)
plt.plot(kx_1, ky_1, 'b-', alpha=0.5, label='Uniform Angle Increment')
plt.scatter(kx_1[::50], ky_1[::50], c='blue', s=30)

plt.plot(kx_2, ky_2, 'r-', alpha=0.5, label='Uniform Radius Increment')
plt.scatter(kx_2[::50], ky_2[::50], c='red', s=30)

plt.plot(kx_3, ky_3, 'g-', alpha=0.5, label='Uniform Arc Length Increment')
plt.scatter(kx_3[::50], ky_3[::50], c='green', s=30)

plt.xlabel('kx (cycles/m)')
plt.ylabel('ky (cycles/m)')
plt.title('Comparison of Three Sampling Methods')
plt.grid(True)
plt.axis('equal')
plt.legend()

# 2. Uniform angle increment - Detail view
plt.subplot(2, 2, 2)
plt.plot(kx_1, ky_1, 'b-', alpha=0.7)

# Mark every 100 points
for i in range(0, num_samples, 100):
    plt.scatter(kx_1[i], ky_1[i], c='red', s=30)
    if i > 0:
        plt.annotate(f'{i}', (kx_1[i], ky_1[i]), fontsize=8)

plt.xlabel('kx (cycles/m)')
plt.ylabel('ky (cycles/m)')
plt.title('Uniform Angle Sampling (Marking Every 100 Points)')
plt.grid(True)
plt.axis('equal')

# 3. Sampling point density in k-space
plt.subplot(2, 2, 3)
k_radii = np.sqrt(kx_1**2 + ky_1**2)
bin_edges = np.linspace(0, max_k, 20)
plt.hist(k_radii, bins=bin_edges, alpha=0.7)
plt.xlabel('k-space Radius (cycles/m)')
plt.ylabel('Number of Samples')
plt.title('Sampling Point Density Distribution')
plt.grid(True)

# 4. Show first 100 sampling points
plt.subplot(2, 2, 4)
sample_indices = np.arange(0, min(100, num_samples))
plt.plot(kx_1, ky_1, 'b-', alpha=0.3)
plt.scatter(kx_1[sample_indices], ky_1[sample_indices], c=sample_indices, cmap='viridis', s=30)
for i in sample_indices[::10]:
    plt.annotate(f'{i}', (kx_1[i], ky_1[i]), fontsize=8)

plt.xlabel('kx (cycles/m)')
plt.ylabel('ky (cycles/m)')
plt.title('First 100 Sampling Points')
plt.grid(True)
plt.axis('equal')
plt.colorbar(label='Sample Index')

plt.tight_layout()
plt.show()

# -----------------------------------------------------------------------------
# Output key parameters
# -----------------------------------------------------------------------------
print(f"FOV: {fov_xy*100:.1f} cm")
print(f"Resolution: {resolution*100:.1f} mm")
print(f"Spiral parameter a: {a:.3f} cycles/radian")
print(f"Max k-space radius: {max_k:.1f} cycles/m")
print(f"Max spiral angle: {max_theta:.1f} rad ({max_theta/(2*np.pi):.1f} turns)")
print(f"Total sampling points: {num_samples}")

import numpy as np
import matplotlib.pyplot as plt

# -----------------------------------------------------------------------------
# Basic Parameter Definition
# -----------------------------------------------------------------------------
fov_xy = 15e-2         # Field of view (m), 15cm
resolution = 2e-3      # Resolution (m), 2mm
max_k = 1/(2*resolution)  # Maximum k-space radius (cycles/m)
num_samples = 4000     # Number of sampling points

# Spiral parameter: k = a·θ
a = 1 / (2 * np.pi * fov_xy)  # Spiral spacing parameter (cycles/radian)

# -----------------------------------------------------------------------------
# Calculate Recommended Parameters Based on Nyquist Criterion
# -----------------------------------------------------------------------------
# Calculate Nyquist distance in k-space
dk_nyquist = 1 / fov_xy

# Circumference at maximum k-space extent
max_circumference = 2 * np.pi * max_k

# Required samples at outer edge for Nyquist
samples_nyquist = max_circumference / dk_nyquist

# Recommended turns for full Nyquist sampling
rec_turns = samples_nyquist / (2 * np.pi)
rec_max_theta = rec_turns * 2 * np.pi

# Print recommended parameters (but don't enforce them)
print("Recommended parameters based on Nyquist criterion:")
print(f"Spiral parameter a: {a:.3f} cycles/radian")
print(f"Recommended turns: {rec_turns:.1f}")
print(f"Recommended max_theta: {rec_max_theta:.1f} rad")
print(f"Sampling ratio vs Nyquist: {num_samples/(samples_nyquist):.2f}")

# -----------------------------------------------------------------------------
# Define Actual Sampling Parameters (user-defined)
# -----------------------------------------------------------------------------
# Option 1: Calculate max_theta based on user-defined turns
desired_turns = 75  # Uncomment and set your desired number of turns
max_theta = desired_turns * 2 * np.pi

# Option 2: Set max_theta directly
# max_theta = 100  # Uncomment and set your desired max_theta in radians

# Option 3: Use a scaling factor of the Nyquist requirement (for compressed sensing)
# undersampling_factor = 0.5  # Uncomment and set your desired undersampling factor
# max_theta = rec_max_theta * undersampling_factor

# Default: Use the required max_theta to reach max_k with given 'a'
# max_theta = max_k / a  # This ensures we reach the maximum k-space radius

# max_theta = 300 # self defined angle in total

# -----------------------------------------------------------------------------
# Generate Uniform Angle Sampling Trajectory
# -----------------------------------------------------------------------------
theta = np.linspace(0, max_theta, num_samples)
k = a * theta
kx = k * np.cos(theta)
ky = k * np.sin(theta)

# Calculate turn numbers for each point
turns = theta / (2 * np.pi)

# -----------------------------------------------------------------------------
# Visualization
# -----------------------------------------------------------------------------
plt.figure(figsize=(12, 10))

# Plot the entire trajectory
plt.plot(kx, ky, 'b-', alpha=0.6, linewidth=1.5)

# Mark sampling points at regular intervals
point_interval = 50  # Mark every 50th point
marked_indices = range(0, num_samples, point_interval)
plt.scatter(kx[marked_indices], ky[marked_indices], c='red', s=30)

# Add labels for turn numbers and theta values at specific points
label_indices = [0, 500, 1000, 2000, 3000, 3999]  # Points to label
for idx in label_indices:
    plt.annotate(
        f"Turn: {turns[idx]:.1f}\nθ: {theta[idx]:.1f} rad", 
        (kx[idx], ky[idx]),
        textcoords="offset points",
        xytext=(5, 5),
        fontsize=8,
        bbox=dict(boxstyle="round,pad=0.3", fc="white", alpha=0.7)
    )

# Add circles to mark full turns
full_turns = np.arange(1, int(max(turns))+1)
for turn in full_turns:
    turn_theta = turn * 2 * np.pi
    turn_k = a * turn_theta
    plt.plot(turn_k * np.cos(np.linspace(0, 2*np.pi, 100)), 
             turn_k * np.sin(np.linspace(0, 2*np.pi, 100)), 
             'g--', alpha=0.5)
    # Label the turn number
    plt.text(turn_k * 1.05, 0, f"{turn}", color='green', fontsize=8)

plt.xlabel('kx (cycles/m)')
plt.ylabel('ky (cycles/m)')
plt.title('Uniform Angle Sampling Spiral Trajectory')
plt.grid(True, alpha=0.3)
plt.axis('equal')

# Add maximum k-space radius circle
theta_circle = np.linspace(0, 2*np.pi, 100)
plt.plot(max_k * np.cos(theta_circle), max_k * np.sin(theta_circle), 'r--', alpha=0.8)
plt.text(0, -max_k*1.1, f"max_k = {max_k:.1f} cycles/m", ha='center', fontsize=10)

plt.tight_layout()
plt.show()

# Print key parameters
print("\nActual parameters used:")
print(f"FOV: {fov_xy*100:.1f} cm")
print(f"Resolution: {resolution*100:.1f} mm")
print(f"Spiral parameter a: {a:.3f} cycles/radian")
print(f'The number of turns: {max_theta/(2*np.pi):.1f}')
print(f"Max k-space radius: {max_k:.1f} cycles/m")
print(f'delta theta = {max_theta/num_samples:.3f} rad')
print(f"Max spiral angle: {max_theta:.1f} rad ({max_theta/(2*np.pi):.1f} turns)")
print(f"Total sampling points: {num_samples}")

import numpy as np
import matplotlib.pyplot as plt

def calculate_gradients_and_slew(kx, ky, readout_time):
    """
    Calculate gradient and slew rate waveforms from k-space trajectory
    
    Parameters:
    -----------
    kx, ky : arrays of k-space coordinates
    readout_time : total readout duration in seconds
    
    Returns:
    --------
    t : time array
    gx, gy : gradient waveforms in T/m
    g_mag : gradient magnitude
    slew_x, slew_y : slew rate waveforms in T/m/s
    slew_mag : slew rate magnitude
    """
    num_points = len(kx)
    t = np.linspace(0, readout_time, num_points)  # Time vector
    dt = t[1] - t[0]  # Time step
    
    # Calculate gradients (T/m)
    gx = np.zeros_like(kx)
    gy = np.zeros_like(ky)
    
    # Central differences for most points
    gx[1:-1] = (kx[2:] - kx[:-2]) / (2 * dt) / gamma
    gy[1:-1] = (ky[2:] - ky[:-2]) / (2 * dt) / gamma
    
    # Edge points
    gx[0] = (kx[1] - kx[0]) / dt / gamma
    gy[0] = (ky[1] - ky[0]) / dt / gamma
    gx[-1] = (kx[-1] - kx[-2]) / dt / gamma
    gy[-1] = (ky[-1] - ky[-2]) / dt / gamma
    
    # Gradient magnitude
    g_mag = np.sqrt(gx**2 + gy**2)
    
    # Calculate slew rates (T/m/s)
    slew_x = np.zeros_like(gx)
    slew_y = np.zeros_like(gy)
    
    # Central differences for most points
    slew_x[1:-1] = (gx[2:] - gx[:-2]) / (2 * dt)
    slew_y[1:-1] = (gy[2:] - gy[:-2]) / (2 * dt)
    
    # Edge points
    slew_x[0] = (gx[1] - gx[0]) / dt
    slew_y[0] = (gy[1] - gy[0]) / dt
    slew_x[-1] = (gx[-1] - gx[-2]) / dt
    slew_y[-1] = (gy[-1] - gy[-2]) / dt
    
    # Slew rate magnitude
    slew_mag = np.sqrt(slew_x**2 + slew_y**2)
    
    return t, gx, gy, g_mag, slew_x, slew_y, slew_mag

def optimize_readout_time_improved(kx, ky, max_grad, max_slew, gamma=42.576e6, safety_factor=0.95):
    """
    优化k空间采样的时间步长，包括对初始阶段应用相同的梯度和斜率约束
    
    Parameters:
    -----------
    kx, ky : k空间轨迹坐标数组
    max_grad : 最大梯度幅值 (T/m)
    max_slew : 最大梯度变化率 (T/m/s)
    gamma : 旋磁比 (Hz/T)
    safety_factor : 安全系数
    
    Returns:
    --------
    t_opt, gx_opt, gy_opt : 优化后的时间和梯度波形
    """
    n_points = len(kx)
    
    # 初始化数组
    t_opt = np.zeros(n_points)
    gx_opt = np.zeros(n_points)
    gy_opt = np.zeros(n_points)
    
    # 最小时间步长（梯度系统光栅时间）
    dt_min = 4e-6  # 4 μs
    
    # 应用安全系数到限制值
    max_grad_safe = max_grad * safety_factor
    max_slew_safe = max_slew * safety_factor
    
    # 第一个点的特殊处理 (i=1)
    dk_x_first = kx[1] - kx[0]
    dk_y_first = ky[1] - ky[0]
    dk_first = np.sqrt(dk_x_first**2 + dk_y_first**2)
    
    # 平滑从零启动: dk = 0.5 * gamma * slew * dt^2
    dt_slew_first = np.sqrt(2 * dk_first / (gamma * max_slew_safe))
    dt_grad_first = dk_first / (gamma * max_grad_safe)
    dt_first = max(dt_slew_first, dt_grad_first, dt_min)
    
    # 应用小的安全裕度 (1%)
    dt_first *= 1.01
    
    # 设置第一个点
    t_opt[1] = dt_first
    gx_opt[1] = dk_x_first / (gamma * dt_first)
    gy_opt[1] = dk_y_first / (gamma * dt_first)
    
    # 修改：对所有后续点应用相同的优化方法，不再单独处理初始阶段
    for i in range(2, n_points):
        # k空间距离
        dk_x = kx[i] - kx[i-1]
        dk_y = ky[i] - ky[i-1]
        dk = np.sqrt(dk_x**2 + dk_y**2)
        
        # 前一个点的梯度
        g_prev_x = gx_opt[i-1]
        g_prev_y = gy_opt[i-1]
        
        # 计算梯度限制所需的时间步长
        dt_grad = dk / (gamma * max_grad_safe)
        
        # 计算梯度变化率限制所需的时间步长
        # 将前一个梯度投影到当前方向，以便更准确计算
        if dk > 0:
            dk_unit_x = dk_x / dk
            dk_unit_y = dk_y / dk
            g_prev_proj = g_prev_x * dk_unit_x + g_prev_y * dk_unit_y
        else:
            g_prev_proj = np.sqrt(g_prev_x**2 + g_prev_y**2)
        
        # 求解二次方程: max_slew*dt^2 + g_prev_proj*dt - dk/gamma >= 0
        a = max_slew_safe
        b = g_prev_proj  
        c = -dk / gamma
        
        # 寻找正根
        discriminant = b**2 - 4*a*c
        if discriminant >= 0:
            dt_slew = (-b + np.sqrt(discriminant)) / (2*a)
        else:
            # 后备方案(有效轨迹不应该出现这种情况)
            dt_slew = dt_min
        
        # 使用最严格的约束
        dt = max(dt_grad, dt_slew, dt_min)
        
        # 应用小的安全裕度 (1%)
        dt *= 1.01
        
        # 计算新的梯度值
        g_new_x = dk_x / (gamma * dt)
        g_new_y = dk_y / (gamma * dt)
        
        # 显式验证斜率限制并调整
        slew_x = (g_new_x - g_prev_x) / dt
        slew_y = (g_new_y - g_prev_y) / dt
        slew_mag = np.sqrt(slew_x**2 + slew_y**2)
        
        # 如果斜率仍超限，进一步调整dt
        if slew_mag > max_slew_safe:
            scaling_factor = slew_mag / max_slew_safe
            scaling_factor = 1.04
            dt *= scaling_factor
            
            # 重新计算梯度
            g_new_x = dk_x / (gamma * dt)
            g_new_y = dk_y / (gamma * dt)

        # 更新时间和梯度数组
        t_opt[i] = t_opt[i-1] + dt
        gx_opt[i] = g_new_x
        gy_opt[i] = g_new_y
    
    return t_opt, gx_opt, gy_opt

def visualize_gradient_analysis(t, kx, ky, gx, gy, g_mag, slew_x, slew_y, slew_mag):
    """Create comprehensive visualization of gradient and slew rate analysis"""
    
    # Define system limits (typical values for a clinical scanner)
    max_grad = 200e-3  # 40 mT/m
    max_slew = 200    # 200 T/m/s
    
    # Create figure with 3x2 subplots
    fig, axs = plt.subplots(3, 2, figsize=(18, 15))
    
    # Plot 1: K-space trajectory
    axs[0, 0].plot(kx, ky)
    axs[0, 0].set_title('K-space Trajectory', fontsize=14)
    axs[0, 0].set_xlabel('kx (cycles/m)')
    axs[0, 0].set_ylabel('ky (cycles/m)')
    axs[0, 0].grid(True)
    axs[0, 0].axis('equal')
    
    # Plot 2: X and Y gradient waveforms
    axs[0, 1].plot(t*1000, gx*1000, 'b-', label='Gx')
    axs[0, 1].plot(t*1000, gy*1000, 'g-', label='Gy')
    axs[0, 1].axhline(y=max_grad*1000, color='r', linestyle='--', label=f'Limit: ±{max_grad*1000:.1f} mT/m')
    axs[0, 1].axhline(y=-max_grad*1000, color='r', linestyle='--')
    axs[0, 1].set_title('Gradient Waveforms', fontsize=14)
    axs[0, 1].set_xlabel('Time (ms)')
    axs[0, 1].set_ylabel('Gradient (mT/m)')
    axs[0, 1].grid(True)
    axs[0, 1].legend()
    
    # Plot 3: Gradient magnitude
    axs[1, 0].plot(t*1000, g_mag*1000)
    axs[1, 0].axhline(y=max_grad*1000, color='r', linestyle='--', label=f'Limit: {max_grad*1000:.1f} mT/m')
    axs[1, 0].set_title('Gradient Magnitude', fontsize=14)
    axs[1, 0].set_xlabel('Time (ms)')
    axs[1, 0].set_ylabel('Gradient Magnitude (mT/m)')
    axs[1, 0].grid(True)
    axs[1, 0].legend()
    
    # Plot 4: X and Y slew rate waveforms
    axs[1, 1].plot(t*1000, slew_x, 'b-', label='Slew X')
    axs[1, 1].plot(t*1000, slew_y, 'g-', label='Slew Y')
    axs[1, 1].axhline(y=max_slew, color='r', linestyle='--', label=f'Limit: ±{max_slew:.1f} T/m/s')
    axs[1, 1].axhline(y=-max_slew, color='r', linestyle='--')
    axs[1, 1].set_title('Slew Rate Waveforms', fontsize=14)
    axs[1, 1].set_xlabel('Time (ms)')
    axs[1, 1].set_ylabel('Slew Rate (T/m/s)')
    axs[1, 1].grid(True)
    axs[1, 1].legend()
    
    # Plot 5: Slew rate magnitude
    axs[2, 0].plot(t*1000, slew_mag)
    axs[2, 0].axhline(y=max_slew, color='r', linestyle='--', label=f'Limit: {max_slew:.1f} T/m/s')
    axs[2, 0].set_title('Slew Rate Magnitude', fontsize=14)
    axs[2, 0].set_xlabel('Time (ms)')
    axs[2, 0].set_ylabel('Slew Rate (T/m/s)')
    axs[2, 0].grid(True)
    axs[2, 0].legend()
    
    # Plot 6: Violations and statistics
    axs[2, 1].axis('off')  # Turn off axes
    
    # Calculate statistics
    max_g_val = np.max(g_mag)*1000
    max_g_percent = max_g_val / (max_grad*1000) * 100
    
    max_slew_val = np.max(slew_mag)
    max_slew_percent = max_slew_val / max_slew * 100
    
    g_violations = np.sum(g_mag*1000 > max_grad*1000)
    slew_violations = np.sum(slew_mag > max_slew)
    
    g_violation_percent = g_violations / len(g_mag) * 100
    slew_violation_percent = slew_violations / len(slew_mag) * 100
    
    # Add text with statistics
    stats_text = (
        f"GRADIENT ANALYSIS\n\n"
        f"Maximum gradient: {max_g_val:.2f} mT/m ({max_g_percent:.1f}% of limit)\n"
        f"Maximum slew rate: {max_slew_val:.2f} T/m/s ({max_slew_percent:.1f}% of limit)\n\n"
        f"Gradient limit violations: {g_violations} points ({g_violation_percent:.2f}%)\n"
        f"Slew rate limit violations: {slew_violations} points ({slew_violation_percent:.2f}%)\n\n"
        f"Time step: {(t[1]-t[0])*1e6:.2f} μs\n"
        f"Total readout time: {t[-1]*1000:.2f} ms\n"
        f"Number of samples: {len(t)}"
    )
    
    axs[2, 1].text(0.05, 0.95, stats_text, fontsize=14, 
                  va='top', ha='left', transform=axs[2, 1].transAxes,
                  bbox=dict(boxstyle="round,pad=0.5", fc="white", ec="gray", alpha=0.8))
    
    # Add general title
    plt.suptitle('Gradient and Slew Rate Analysis for Spiral Trajectory', fontsize=16)
    
    plt.tight_layout(rect=[0, 0, 1, 0.97])  # Adjust layout for suptitle
    plt.show()
    
    # Return summary metrics
    return {
        'max_gradient': max_g_val,
        'max_gradient_percent': max_g_percent,
        'max_slew': max_slew_val,
        'max_slew_percent': max_slew_percent,
        'gradient_violations': g_violations,
        'slew_violations': slew_violations
    }

def compare_original_vs_optimized(kx, ky, t_orig, gx_orig, gy_orig, g_mag_orig, slew_mag_orig,
                                  t_opt, gx_opt, gy_opt, g_mag_opt, slew_mag_opt):
    """
    Create visualization comparing original and optimized trajectories
    
    Parameters:
    -----------
    kx, ky : arrays of k-space coordinates
    t_orig, gx_orig, gy_orig, g_mag_orig, slew_mag_orig : original trajectory data
    t_opt, gx_opt, gy_opt, g_mag_opt, slew_mag_opt : optimized trajectory data
    """
    # Define system limits
    max_grad = 40e-3  # 40 mT/m
    max_slew = 200    # 200 T/m/s
    
    # Create figure with 3x2 subplots
    fig, axs = plt.subplots(3, 2, figsize=(18, 15))
    
    # Plot 1: K-space trajectory with timing markers
    axs[0, 0].plot(kx, ky, 'b-', alpha=0.7)
    # Add markers at regular intervals to show timing
    markers_orig = np.linspace(0, len(t_orig)-1, 10).astype(int)
    markers_opt = np.linspace(0, len(t_opt)-1, 10).astype(int)
    axs[0, 0].plot(kx[markers_orig], ky[markers_orig], 'ro', label='Original timing')
    axs[0, 0].plot(kx[markers_opt], ky[markers_opt], 'go', label='Optimized timing')
    axs[0, 0].set_title('K-space Trajectory with Timing Markers', fontsize=14)
    axs[0, 0].set_xlabel('kx (cycles/m)')
    axs[0, 0].set_ylabel('ky (cycles/m)')
    axs[0, 0].grid(True)
    axs[0, 0].axis('equal')
    axs[0, 0].legend()
    
    # Plot 2: k-space coverage over time
    k_mag = np.sqrt(kx**2 + ky**2)
    axs[0, 1].plot(t_orig*1000, k_mag[:len(t_orig)], 'r-', label='Original')
    axs[0, 1].plot(t_opt*1000, k_mag[:len(t_opt)], 'g-', label='Optimized')
    axs[0, 1].set_title('k-space Coverage vs Time', fontsize=14)
    axs[0, 1].set_xlabel('Time (ms)')
    axs[0, 1].set_ylabel('k-space Radius (cycles/m)')
    axs[0, 1].grid(True)
    axs[0, 1].legend()
    
    # Plot 3: Gradient magnitude comparison
    axs[1, 0].plot(t_orig*1000, g_mag_orig*1000, 'r-', label='Original')
    axs[1, 0].plot(t_opt*1000, g_mag_opt*1000, 'g-', label='Optimized')
    axs[1, 0].axhline(y=max_grad*1000, color='k', linestyle='--', label=f'Limit: {max_grad*1000:.1f} mT/m')
    axs[1, 0].set_title('Gradient Magnitude Comparison', fontsize=14)
    axs[1, 0].set_xlabel('Time (ms)')
    axs[1, 0].set_ylabel('Gradient (mT/m)')
    axs[1, 0].grid(True)
    axs[1, 0].legend()
    
    # Plot 4: Slew rate magnitude comparison
    axs[1, 1].plot(t_orig*1000, slew_mag_orig, 'r-', label='Original')
    axs[1, 1].plot(t_opt*1000, slew_mag_opt, 'g-', label='Optimized')
    axs[1, 1].axhline(y=max_slew, color='k', linestyle='--', label=f'Limit: {max_slew:.1f} T/m/s')
    axs[1, 1].set_title('Slew Rate Magnitude Comparison', fontsize=14)
    axs[1, 1].set_xlabel('Time (ms)')
    axs[1, 1].set_ylabel('Slew Rate (T/m/s)')
    axs[1, 1].grid(True)
    axs[1, 1].legend()
    
    # Plot 5: Hardware utilization - Gradient
    g_util_orig = g_mag_orig / max_grad * 100
    g_util_opt = g_mag_opt / max_grad * 100
    axs[2, 0].plot(t_orig*1000, g_util_orig, 'r-', label='Original - Gradient')
    axs[2, 0].plot(t_opt*1000, g_util_opt, 'g-', label='Optimized - Gradient')
    axs[2, 0].axhline(y=100, color='k', linestyle='--', label='Limit')
    axs[2, 0].set_title('Gradient Utilization', fontsize=14)
    axs[2, 0].set_xlabel('Time (ms)')
    axs[2, 0].set_ylabel('Gradient Utilization (%)')
    axs[2, 0].grid(True)
    axs[2, 0].legend()
    
    # Plot 6: Hardware utilization - Slew Rate
    s_util_orig = slew_mag_orig / max_slew * 100
    s_util_opt = slew_mag_opt / max_slew * 100
    axs[2, 1].plot(t_orig*1000, s_util_orig, 'r-', label='Original - Slew')
    axs[2, 1].plot(t_opt*1000, s_util_opt, 'g-', label='Optimized - Slew')
    axs[2, 1].axhline(y=100, color='k', linestyle='--', label='Limit')
    axs[2, 1].set_title('Slew Rate Utilization', fontsize=14)
    axs[2, 1].set_xlabel('Time (ms)')
    axs[2, 1].set_ylabel('Slew Rate Utilization (%)')
    axs[2, 1].grid(True)
    axs[2, 1].legend()
    
    # Calculate statistics for text box
    time_reduction = (1 - t_opt[-1]/t_orig[-1]) * 100
    avg_g_util_orig = np.mean(g_util_orig)
    avg_g_util_opt = np.mean(g_util_opt)
    avg_s_util_orig = np.mean(s_util_orig)
    avg_s_util_opt = np.mean(s_util_opt)
    
    # Add optimization summary as a figure title
    summary_text = (
        f"Readout Time Optimization Results\n"
        f"Original: {t_orig[-1]*1000:.2f} ms → Optimized: {t_opt[-1]*1000:.2f} ms "
        f"({time_reduction:.1f}% reduction)"
    )
    fig.suptitle(summary_text, fontsize=16)
    
    plt.tight_layout(rect=[0, 0, 1, 0.97])  # Adjust layout for suptitle
    plt.show()
    
    # Print summary statistics
    print("\nOptimization Results Summary:")
    print("-" * 80)
    print(f"Original readout time: {t_orig[-1]*1000:.2f} ms")
    print(f"Optimized readout time: {t_opt[-1]*1000:.2f} ms")
    print(f"Time reduction: {time_reduction:.1f}%")
    print(f"Average gradient utilization: {avg_g_util_orig:.1f}% → {avg_g_util_opt:.1f}%")
    print(f"Average slew rate utilization: {avg_s_util_orig:.1f}% → {avg_s_util_opt:.1f}%")
    print(f"Maximum gradient: {np.max(g_mag_orig)*1000:.1f} → {np.max(g_mag_opt)*1000:.1f} mT/m")
    print(f"Maximum slew rate: {np.max(slew_mag_orig):.1f} → {np.max(slew_mag_opt):.1f} T/m/s")

# Assumed system and physical constants
gamma = 42.576e6  # Hz/T

# Define system limits
max_grad = 200e-3  # 40 mT/m
max_slew = 200    # 200 T/m/s

# Original readout time
readout_time = 50e-3  # 25 ms

# Calculate original gradient and slew rate waveforms
t_orig, gx_orig, gy_orig, g_mag_orig, slew_x_orig, slew_y_orig, slew_mag_orig = calculate_gradients_and_slew(kx, ky, readout_time)

# Visualize original gradient analysis
print("Analyzing original trajectory...")
metrics_orig = visualize_gradient_analysis(t_orig, kx, ky, gx_orig, gy_orig, g_mag_orig, slew_x_orig, slew_y_orig, slew_mag_orig)

# Apply optimization to find optimal timing
print("\nOptimizing readout time...")
t_opt, gx_opt, gy_opt = optimize_readout_time_improved(kx, ky, max_grad, max_slew)

# Calculate slew rates for the optimized trajectory
g_mag_opt = np.sqrt(gx_opt**2 + gy_opt**2)
slew_x_opt = np.zeros_like(gx_opt)
slew_y_opt = np.zeros_like(gy_opt)
slew_mag_opt = np.zeros_like(gx_opt)

# Calculate slew rates (central differences)
dt_opt = np.diff(t_opt)
for i in range(1, len(t_opt)-1):
    dt_prev = t_opt[i] - t_opt[i-1]
    dt_next = t_opt[i+1] - t_opt[i]
    slew_x_opt[i] = (gx_opt[i+1] - gx_opt[i-1]) / (dt_prev + dt_next)
    slew_y_opt[i] = (gy_opt[i+1] - gy_opt[i-1]) / (dt_prev + dt_next)

print(f'initial dt before optimization 1: {t_orig[1] - t_orig[0]}')
print(f'initial dt after optimization 1: {t_opt[1] - t_opt[0]}')
print(f'initial dt after optimization 2: {t_opt[1] - t_opt[0]}')
# Edge points
if len(t_opt) > 1:
    slew_x_opt[0] = (gx_opt[1] - gx_opt[0]) / (t_opt[1] - t_opt[0])
    slew_y_opt[0] = (gy_opt[1] - gy_opt[0]) / (t_opt[1] - t_opt[0])
    slew_x_opt[0:10] = 0
    slew_y_opt[0:10] = 0
    slew_x_opt[-1] = (gx_opt[-1] - gx_opt[-2]) / (t_opt[-1] - t_opt[-2])
    slew_y_opt[-1] = (gy_opt[-1] - gy_opt[-2]) / (t_opt[-1] - t_opt[-2])
    slew_x_opt[-1] = 0
    slew_y_opt[-1] = 0



# Calculate slew magnitude
slew_mag_opt = np.sqrt(slew_x_opt**2 + slew_y_opt**2)
print(f"size of slew: {slew_mag_opt.size}")
# Visualize optimized gradient analysis
print("\nAnalyzing optimized trajectory...")
metrics_opt = visualize_gradient_analysis(t_opt, kx, ky, gx_opt, gy_opt, g_mag_opt, slew_x_opt, slew_y_opt, slew_mag_opt)

# Compare original vs optimized trajectories
compare_original_vs_optimized(kx, ky, t_orig, gx_orig, gy_orig, g_mag_orig, slew_mag_orig,
                             t_opt, gx_opt, gy_opt, g_mag_opt, slew_mag_opt)

import numpy as np
import matplotlib.pyplot as plt

def qdf(a, b, c):
    """
    Solves quadratic equation ax^2 + bx + c = 0
    
    Returns:
        Roots of the quadratic equation
    """
    d = b**2 - 4*a*c
    if d < 0:
        # Handle negative discriminant case
        d = complex(d, 0)
    root1 = (-b + np.sqrt(d)) / (2*a)
    root2 = (-b - np.sqrt(d)) / (2*a)
    return np.array([root1, root2])

def findq2r2(smax, gmax, r, r1, T, Ts, N, Fcoeff, rmax, z=0):
    """
    Calculates the second derivatives of r and theta (q) that satisfy hardware and FOV constraints
    
    Parameters:
        smax: Maximum slew rate in G/cm/s
        gmax: Maximum gradient amplitude in G/cm
        r: Current value of the k-space radius
        r1: Current derivative of r
        T: Gradient sample period
        Ts: Data sampling period
        N: Number of spiral interleaves
        Fcoeff: FOV coefficients for variable density
        rmax: Maximum k-space radius
        z: R/L for gradient coil (voltage model parameter)
    
    Returns:
        q2: Second derivative of angle theta
        r2: Second derivative of radius r
    """
    gamma = 4258  # Hz/G

    smax = smax + z*gmax

    # Calculate FOV and its derivative for current r
    F = 0
    dFdr = 0
    for rind in range(len(Fcoeff)):
        F += Fcoeff[rind] * (r/rmax)**(rind)
        if rind > 0:
            dFdr += rind * Fcoeff[rind] * (r/rmax)**(rind-1) / rmax

    # FOV limit on gradient
    GmaxFOV = 1/gamma / F / Ts
    Gmax = min(GmaxFOV, gmax)

    # Maximum allowed r1 based on gradient amplitude limit
    maxr1 = np.sqrt((gamma*Gmax)**2 / (1 + (2*np.pi*F*r/N)**2))

    if r1 > maxr1:
        # Gradient amplitude limited case
        r2 = (maxr1 - r1) / T
    else:
        # Slew rate limited case
        twopiFoN = 2*np.pi*F/N
        twopiFoN2 = twopiFoN**2

        # Coefficients for the quadratic equation in r2
        A = 1 + twopiFoN2*r*r
        B = 2*twopiFoN2*r*r1*r1 + 2*twopiFoN2/F*dFdr*r*r*r1*r1 + 2*z*r1 + 2*twopiFoN2*r1*r
        C1 = twopiFoN2**2*r*r*r1**4 + 4*twopiFoN2*r1**4 + (2*np.pi/N*dFdr)**2*r*r*r1**4 + 4*twopiFoN2/F*dFdr*r*r1**4 - (gamma)**2*smax**2
        C2 = z*(z*r1**2 + z*twopiFoN2*r1**2 + 2*twopiFoN2*r1**3*r + 2*twopiFoN2/F*dFdr*r1**3*r)
        C = C1 + C2

        # Solve quadratic equation
        rts = qdf(A, B, C)
        r2 = np.real(rts[0])  # Use first root

        # Calculate and check resulting slew rate
        slew = 1/gamma * (r2 - twopiFoN2*r*r1**2 + 1j*twopiFoN*(2*r1**2 + r*r2 + dFdr/F*r*r1**2))
        sr = np.abs(slew)/smax

        if sr > 1.01:
            print(f"Slew violation, slew = {round(np.abs(slew))}, smax = {round(smax)}, sr={sr:.3f}, r={r:.3f}, r1={r1:.3f}")

    # Calculate q2 from other parameters
    q2 = 2*np.pi/N*dFdr*r1**2 + 2*np.pi*F/N*r2
    
    return q2, r2

def vds(smax, gmax, T, N, Fcoeff, rmax, z=0):
    """
    Variable Density Spiral trajectory generation
    
    Parameters:
        smax: Maximum slew rate G/cm/s
        gmax: Maximum gradient G/cm
        T: Sampling period (s)
        N: Number of interleaves
        Fcoeff: FOV coefficients - FOV(r) = Sum_k Fcoeff[k]*(r/rmax)^k
        rmax: Maximum k-space radius (cm^-1)
        z: R/L for gradient coil model
        
    Returns:
        k: k-space trajectory (kx+iky) in cm^-1
        g: gradient waveform (Gx+iGy) in G/cm
        s: derivative of g (Sx+iSy) in G/cm/s
        time: time points corresponding to trajectory (s)
        r: k-space radius vs time
        theta: angle vs time
    """
    print('Variable Density Spiral Generation')
    gamma = 4258  # Hz/G

    # Oversampling for trajectory calculation
    oversamp = 8  # Keep this even
    To = T / oversamp  # Oversampled period

    # Initialize variables
    q0 = 0
    q1 = 0
    r0 = 0
    r1 = 0
    t = 0
    count = 0

    # Pre-allocate arrays (can extend later if needed)
    max_points = 1000000
    theta = np.zeros(max_points)
    r = np.zeros(max_points)
    time = np.zeros(max_points)

    # Main loop to generate trajectory
    while r0 < rmax:
        # Get the next point on the trajectory
        q2, r2 = findq2r2(smax, gmax, r0, r1, To, T, N, Fcoeff, rmax, z)

        # Integrate for θ, θ', r, and r'
        q1 = q1 + q2 * To
        q0 = q0 + q1 * To
        t = t + To

        r1 = r1 + r2 * To
        r0 = r0 + r1 * To

        # Store values
        count += 1
        theta[count] = q0
        r[count] = r0
        time[count] = t

        if count % 100 == 0:
            print(f'{count} points, |k|={r0:.6f}')

        # Break if we've reached array limit
        if count >= max_points - 1:
            print("Warning: reached maximum array size")
            break

    # Trim arrays to used size
    theta = theta[:count+1]
    r = r[:count+1]
    time = time[:count+1]

    # Downsample to original sampling rate
    theta_ds = theta[oversamp//2::oversamp]
    r_ds = r[oversamp//2::oversamp]
    time_ds = time[oversamp//2::oversamp]

    # Keep the length a multiple of 4 (to match original code)
    length = 4 * (len(theta_ds) // 4)
    theta_ds = theta_ds[:length]
    r_ds = r_ds[:length]
    time_ds = time_ds[:length]

    # Calculate k-space trajectory, gradients, and slew rates
    k = r_ds * np.exp(1j * theta_ds)
    
    # Calculate gradients
    g = np.zeros_like(k, dtype=complex)
    g[:-1] = (k[1:] - k[:-1]) / T / gamma
    g[-1] = g[-2]  # Extrapolate last point
    
    # Calculate slew rates
    s = np.zeros_like(g, dtype=complex)
    s[:-1] = (g[1:] - g[:-1]) / T
    s[-1] = s[-2]  # Extrapolate last point

    # Plot results
    plot_vds_results(time_ds, k, g, s)

    return k, g, s, time_ds, r_ds, theta_ds

def plot_vds_results(time, k, g, s):
    """
    Plot the results of the VDS trajectory generation
    """
    # Undersample for plotting
    tp = time[::10]
    kp = k[::10]
    gp = g[::10]
    sp = s[::10]
    
    plt.figure(figsize=(12, 10))
    
    # Plot 1: k-space trajectory
    plt.subplot(2, 2, 1)
    plt.plot(np.real(kp), np.imag(kp))
    plt.title('ky vs kx')
    plt.xlabel('kx (cm$^{-1}$)')
    plt.ylabel('ky (cm$^{-1}$)')
    plt.axis('square')
    plt.grid(True)
    
    # Plot 2: k-space vs time
    plt.subplot(2, 2, 2)
    plt.plot(tp, np.real(kp), 'c-', label='kx')
    plt.plot(tp, np.imag(kp), 'g-', label='ky')
    plt.title('k-space vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('k (cm$^{-1}$)')
    plt.legend()
    plt.grid(True)
    
    # Plot 3: Gradient vs time
    plt.subplot(2, 2, 3)
    plt.plot(tp, np.real(gp), 'c-', label='Gx')
    plt.plot(tp, np.imag(gp), 'g-', label='Gy')
    plt.title('Gradient vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('g (G/cm)')
    plt.legend()
    plt.grid(True)
    
    # Plot 4: Slew rate vs time
    plt.subplot(2, 2, 4)
    plt.plot(tp, np.real(sp), 'c-', label='Sx')
    plt.plot(tp, np.imag(sp), 'g-', label='Sy')
    plt.plot(tp, np.abs(sp), 'k-', label='|S|')
    plt.title('Slew-Rate vs Time')
    plt.xlabel('Time (s)')
    plt.ylabel('s (G/cm/s)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

# Example parameters
smax = 20000       # Maximum slew rate (G/cm/s) 200T/m/s
gmax = 4           # Maximum gradient amplitude (G/cm) 40 mt/m
T = 4e-6           # Sampling period (s)
N = 1              # Number of interleaves
Fcoeff = [18, 0]   # FOV = 20 cm (constant)
rmax = 1/(2*0.2)   # Maximum k-space radius for 1mm resolution

# Example with variable density
# FOV = 24 - 4*(r/rmax) cm (decreases from 24cm to 20cm)
# Fcoeff = [24, -4]


# Generate the spiral trajectory
k, g, s, time, r, theta = vds(smax, gmax, T, N, Fcoeff, rmax)
print(f'matrix size of kspace: {k.shape}')
print(f'maximum radial distance from kspace center: {r.max()}')
print(f'minimum read out time: {time[-1]*1000} ms')

# -----------------------------------------------------------------------------
# Calculate Gradients and Slew Rates for All Three Sampling Methods
# -----------------------------------------------------------------------------
# MR system parameters
gamma = 42.58e6  # Hz/T, gyromagnetic ratio

# Define readout time for all spirals
readout_time = 40e-3  # 5 ms

# Hardware limits for reference
max_grad = 80e-3  # 40 mT/m = 4 G/cm
max_slew = 200    # 150 T/m/s = 150 mT/m/ms

# Function to calculate gradients and slew rates
def calculate_gradients_and_slew(kx, ky, readout_time):
    num_points = len(kx)
    t = np.linspace(0, readout_time, num_points)  # Time vector
    dt = t[1] - t[0]  # Time step
    
    # Calculate gradients (T/m)
    gx = np.zeros_like(kx)
    gy = np.zeros_like(ky)
    
    # Central differences for most points
    gx[1:-1] = (kx[2:] - kx[:-2]) / (2 * dt) / gamma
    gy[1:-1] = (ky[2:] - ky[:-2]) / (2 * dt) / gamma
    
    # Edge points
    gx[0] = (kx[1] - kx[0]) / dt / gamma
    gy[0] = (ky[1] - ky[0]) / dt / gamma
    gx[-1] = (kx[-1] - kx[-2]) / dt / gamma
    gy[-1] = (ky[-1] - ky[-2]) / dt / gamma
    
    # Gradient magnitude
    g_mag = np.sqrt(gx**2 + gy**2)
    
    # Calculate slew rates (T/m/s)
    slew_x = np.zeros_like(gx)
    slew_y = np.zeros_like(gy)
    
    # Central differences for most points
    slew_x[1:-1] = (gx[2:] - gx[:-2]) / (2 * dt)
    slew_y[1:-1] = (gy[2:] - gy[:-2]) / (2 * dt)
    
    # Edge points
    slew_x[0] = (gx[1] - gx[0]) / dt
    slew_y[0] = (gy[1] - gy[0]) / dt
    slew_x[-1] = (gx[-1] - gx[-2]) / dt
    slew_y[-1] = (gy[-1] - gy[-2]) / dt
    
    # Slew rate magnitude
    slew_mag = np.sqrt(slew_x**2 + slew_y**2)
    
    return t, gx, gy, g_mag, slew_x, slew_y, slew_mag

# Calculate for all three methods
t1, gx1, gy1, g_mag1, slew_x1, slew_y1, slew_mag1 = calculate_gradients_and_slew(kx_1, ky_1, readout_time)
t2, gx2, gy2, g_mag2, slew_x2, slew_y2, slew_mag2 = calculate_gradients_and_slew(kx_2, ky_2, readout_time)
t3, gx3, gy3, g_mag3, slew_x3, slew_y3, slew_mag3 = calculate_gradients_and_slew(kx_3, ky_3, readout_time)

# -----------------------------------------------------------------------------
# Visualization
# -----------------------------------------------------------------------------
plt.figure(figsize=(18, 12))

# 1. Gradient magnitude comparison
plt.subplot(2, 3, 1)
plt.plot(t1*1000, g_mag1*1000, 'b-', label='Uniform Angle')
plt.plot(t2*1000, g_mag2*1000, 'r-', label='Uniform Radius')
plt.plot(t3*1000, g_mag3*1000, 'g-', label='Uniform Arc Length')
plt.axhline(y=max_grad*1000, color='k', linestyle='--', label=f'Max: {max_grad*1000:.0f} mT/m')
plt.xlabel('Time (ms)')
plt.ylabel('Gradient Magnitude (mT/m)')
plt.title('Gradient Magnitude vs Time')
plt.grid(True)
plt.legend()

# 2. X gradient component comparison
plt.subplot(2, 3, 2)
plt.plot(t1*1000, gx1*1000, 'b-', label='Uniform Angle')
plt.plot(t2*1000, gx2*1000, 'r-', label='Uniform Radius')
plt.plot(t3*1000, gx3*1000, 'g-', label='Uniform Arc Length')
plt.axhline(y=max_grad*1000, color='k', linestyle='--')
plt.axhline(y=-max_grad*1000, color='k', linestyle='--')
plt.xlabel('Time (ms)')
plt.ylabel('Gx (mT/m)')
plt.title('X Gradient vs Time')
plt.grid(True)
plt.legend()

# 3. Y gradient component comparison
plt.subplot(2, 3, 3)
plt.plot(t1*1000, gy1*1000, 'b-', label='Uniform Angle')
plt.plot(t2*1000, gy2*1000, 'r-', label='Uniform Radius')
plt.plot(t3*1000, gy3*1000, 'g-', label='Uniform Arc Length')
plt.axhline(y=max_grad*1000, color='k', linestyle='--')
plt.axhline(y=-max_grad*1000, color='k', linestyle='--')
plt.xlabel('Time (ms)')
plt.ylabel('Gy (mT/m)')
plt.title('Y Gradient vs Time')
plt.grid(True)
plt.legend()

# 4. Slew magnitude comparison
plt.subplot(2, 3, 4)
plt.plot(t1*1000, slew_mag1, 'b-', label='Uniform Angle')
plt.plot(t2*1000, slew_mag2, 'r-', label='Uniform Radius')
plt.plot(t3*1000, slew_mag3, 'g-', label='Uniform Arc Length')
plt.axhline(y=max_slew, color='k', linestyle='--', label=f'Max: {max_slew:.0f} T/m/s')
plt.xlabel('Time (ms)')
plt.ylabel('Slew Rate (T/m/s)')
plt.title('Slew Rate Magnitude vs Time')
plt.grid(True)
plt.legend()

# 5. X slew component comparison
plt.subplot(2, 3, 5)
plt.plot(t1*1000, slew_x1, 'b-', label='Uniform Angle')
plt.plot(t2*1000, slew_x2, 'r-', label='Uniform Radius')
plt.plot(t3*1000, slew_x3, 'g-', label='Uniform Arc Length')
plt.axhline(y=max_slew, color='k', linestyle='--')
plt.axhline(y=-max_slew, color='k', linestyle='--')
plt.xlabel('Time (ms)')
plt.ylabel('Slew X (T/m/s)')
plt.title('X Slew Rate vs Time')
plt.grid(True)
plt.legend()

# 6. Y slew component comparison
plt.subplot(2, 3, 6)
plt.plot(t1*1000, slew_y1, 'b-', label='Uniform Angle')
plt.plot(t2*1000, slew_y2, 'r-', label='Uniform Radius')
plt.plot(t3*1000, slew_y3, 'g-', label='Uniform Arc Length')
plt.axhline(y=max_slew, color='k', linestyle='--')
plt.axhline(y=-max_slew, color='k', linestyle='--')
plt.xlabel('Time (ms)')
plt.ylabel('Slew Y (T/m/s)')
plt.title('Y Slew Rate vs Time')
plt.grid(True)
plt.legend()

plt.tight_layout()
plt.show()

# -----------------------------------------------------------------------------
# Summary statistics
# -----------------------------------------------------------------------------
# Create a comparison table
methods = ["Uniform Angle", "Uniform Radius", "Uniform Arc Length"]
max_grads = [np.max(g_mag1)*1000, np.max(g_mag2)*1000, np.max(g_mag3)*1000]
max_slews = [np.max(slew_mag1), np.max(slew_mag2), np.max(slew_mag3)]

print("Gradient and Slew Rate Summary:")
print("-" * 80)
print(f"{'Method':<20} | {'Max Gradient (mT/m)':<25} | {'Max Slew Rate (T/m/s)':<25}")
print("-" * 80)
for i in range(3):
    grad_status = "EXCEEDS LIMIT!" if max_grads[i] > max_grad*1000 else "within limit"
    slew_status = "EXCEEDS LIMIT!" if max_slews[i] > max_slew else "within limit"
    print(f"{methods[i]:<20} | {max_grads[i]:.2f} ({grad_status}) | {max_slews[i]:.2f} ({slew_status})")

print("-" * 80)
print(f"Hardware limits: {max_grad*1000:.2f} mT/m, {max_slew:.2f} T/m/s")
print(f"Readout time: {readout_time*1000:.2f} ms")